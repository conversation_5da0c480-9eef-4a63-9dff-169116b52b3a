"use client"

import { createContext, useState, ReactNode } from "react"

interface NewDoctorContextProps {
  newDoctor: {
    firstName: string;
    lastName: string;
    mn: string;
    specialties: string[];
    title: "Dr" | "<PERSON><PERSON>";
    email: string;
    phone: string;
    dni: string; }
  setNewDoctor: (doctor: {
    firstName: string;
    lastName: string;
    mn: string;
    specialties: string[];
    title: "Dr" | "Dra";
    email: string;
    phone: string;
    dni: string; }) => void
  isNewDoctorDialogOpen: boolean
  setIsNewDoctorDialogOpen: (open: boolean) => void
  newSpecialty: string
  setNewSpecialty: (specialty: string) => void
  addSpecialty: () => void
  removeSpecialty: (specialty: string) => void
}

export const NewDoctorContext = createContext<NewDoctorContextProps>({} as NewDoctorContextProps)

export function NewDoctorProvider({ children }: { children: ReactNode }) {
  const [newDoctor, setNewDoctor] = useState({
    firstName: "",
    lastName: "",
    mn: "",
    specialties: [] as string[],
    title: "Dr" as "Dr" | "Dra",
    email: "",
    phone: "",
    dni: "",
  })
  const [isNewDoctorDialogOpen, setIsNewDoctorDialogOpen] = useState(false)
  const [newSpecialty, setNewSpecialty] = useState("")

  const addSpecialty = () => {
    if (newSpecialty && !newDoctor.specialties.includes(newSpecialty)) {
      setNewDoctor({ ...newDoctor, specialties: [...newDoctor.specialties, newSpecialty] })
      setNewSpecialty("")
    }
  }

  const removeSpecialty = (specialty: string) => {
    setNewDoctor({ ...newDoctor, specialties: newDoctor.specialties.filter((s) => s !== specialty) })
  }

  return (
    <NewDoctorContext.Provider
      value={{
        newDoctor,
        setNewDoctor,
        isNewDoctorDialogOpen,
        setIsNewDoctorDialogOpen,
        newSpecialty,
        setNewSpecialty,
        addSpecialty,
        removeSpecialty,
      }}
    >
      {children}
    </NewDoctorContext.Provider>
  )
}