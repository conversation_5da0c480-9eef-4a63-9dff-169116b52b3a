# Turnera Medical Scheduler

![<PERSON><PERSON> Logo](public/images/turnera-logo.svg)

## Overview

Turnera is a double-sided marketplace for medical appointments, connecting healthcare providers with patients. This repository contains the medical personnel platform where doctors and medical staff can manage their availability, appointments, and patient information.

The complementary patient-facing platform (not included in this repository) allows patients to search for and book appointments with medical professionals.

## Requirements

### System Requirements

- Node.js 18+
- npm or yarn
- Modern web browser with JavaScript enabled

### Environment Variables

The application requires the following environment variables:

```
# .env.local example
NEXT_PUBLIC_API_URL=https://your-api-endpoint.com
NEXT_PUBLIC_STORAGE_PREFIX=turnera
```

## Tech Stack

- **Framework**: [Next.js 15](https://nextjs.org/) with TypeScript
- **UI Components**: 
  - Custom UI components with Radix UI primitives
  - MUI components (Material UI)
  - Styled with Tailwind CSS
- **State Management**: 
  - React Context API
  - Real-time cross-tab synchronization via Storage Events
- **Styling**: 
  - Tailwind CSS
  - Emotion/Styled Components (via MUI)
- **Date Handling**: date-fns
- **Data Visualization**: Recharts
- **HTTP Client**: Axios
- **Notifications**: React Toastify
- **Animations**: Framer Motion
- **Build System**: Turbopack

## Key Features

- **Doctor Management**: Add, edit, and remove doctors with specialties
- **Schedule Management**: Configure working hours, exceptions, and appointment time slots
- **Appointment Booking**: Create, manage, and track appointments
- **Patient Records**: Maintain patient information and appointment history
- **Medical Coverage Integration**: Handle health insurance plans and co-pays
- **Consultation Types**: Define different types of consultations with varying durations and prices
- **Overbooking Configuration**: Configure dynamic scheduling with overbooking options
- **Multi-Establishment Support**: Manage multiple medical centers from a single platform
- **Real-Time Updates**: Synchronize data across multiple browser tabs automatically
- **Consistent Doctor References**: Maintain doctor identity across medical centers

## Directory Structure

```
medical-scheduler/
├── app/                    # Next.js app directory (pages)
│   ├── admin/             # Admin panel and configuration
│   │   ├── layout.tsx     # Admin layout wrapper
│   │   ├── page.tsx       # Main admin dashboard
│   │   └── reset.tsx      # Admin reset functionality
│   ├── plataforma/        # Main platform pages
│   │   ├── establecimiento/ # Medical center management
│   │   │   └── [medicalCenterId]/ # Dynamic medical center routes
│   │   │       ├── [doctorId]/   # Doctor-specific views
│   │   │       ├── analytics/    # Center analytics
│   │   │       ├── configuration/ # Center settings
│   │   │       └── general/      # General information
│   │   ├── profesional/   # Doctor platform
│   │   │   └── [doctorId]/ # Doctor-specific routes
│   │   │       ├── agenda/  # Schedule management
│   │   │       └── analytics/ # Doctor analytics
│   │   └── login/         # Authentication pages
│   ├── reset/             # Password reset functionality
│   ├── layout.tsx         # Root layout component
│   └── page.tsx           # Landing page
├── components/            # Reusable React components
│   ├── admin/            # Admin-specific components
│   │   └── configuration/ # Admin config components
│   │       ├── AdminBulkCreateForms.tsx
│   │       ├── MultiDoctorForm.tsx
│   │       └── MultiMedicalCenterForm.tsx
│   ├── configuration/    # General configuration components
│   │   ├── AddCoverageDialog.tsx
│   │   ├── CoverageCard.tsx
│   │   ├── DoctorCard.tsx
│   │   ├── DoctorDialog.tsx
│   │   └── MedicalCenterConfigCard.tsx
│   ├── ui/               # Base UI components
│   ├── dashboard/        # Dashboard components
│   ├── doctorscheduleviews/ # Doctor schedule views
│   ├── schedulecomponents/  # Schedule-related components
│   └── generalscheduleviews/ # General schedule views
├── contexts/             # React Context providers
│   ├── AppStateContext  # Global UI state management
│   ├── DoctorContext    # Doctor data and operations
│   └── MedicalCenterContext # Medical center management
├── data/                 # Static data and configurations
│   ├── initialData.ts    # Initial doctors and centers data
│   ├── consultationTypes.ts # Predefined consultation types
│   └── specialties.ts    # Medical specialties list
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries and helpers
├── public/               # Static assets and images
│   └── images/          # Image assets including logos
├── services/             # API and data services
│   └── storage.ts       # Local storage service
├── styles/               # Global styles and Tailwind config
├── types/                # TypeScript type definitions
└── utils/                # Utility functions and helpers
```

This structure represents a Next.js application with a focus on medical appointment scheduling and management. Each directory serves a specific purpose in the application's architecture:

## Core Components

### Components

- **UI Components** (`components/ui/`): Basic UI elements like buttons, inputs, cards, etc.
- **Dashboard Components** (`components/dashboard/`): Components for the main dashboard views.
- **Configuration Components** (`components/configuration/`): Forms and interfaces for system configuration.
- **Doctor Schedule Views** (`components/doctorscheduleviews/`): Views specific to doctor schedules.
- **Schedule Components** (`components/schedulecomponents/`): Reusable components for schedule displays and interactions.
- **General Schedule Views** (`components/generalscheduleviews/`): Overall scheduling views for the medical center.

### Contexts

- **AppContext**: 
  - Purpose: Composes all providers into a single tree.
  - Key Exports: `AppProvider`.
  - Connections: Nests all contexts in the proper order: AppStateProvider → MedicalCenterProvider → DoctorProvider → ScheduleProvider → ConsultationProvider → CoverageProvider → NewDoctorProvider.
  - Ensures proper data flow between nested contexts.

- **AppStateContext**: 
  - Purpose: Manages UI state across the application.
  - Key Exports: `activeConfigTab`, `setActiveConfigTab`.
  - Connections: Used by DoctorDialog for tab control and by DoctorContext to reset active tab.
  - State: useState for activeConfigTab.

- **AppointmentContext**: 
  - Purpose: Manages appointment data and operations.
  - Key Exports: `appointments`, `addAppointment`, `removeAppointment`, `updateAppointment`, `patients`, `setPatients`, `addPatient`, `navigationState`, `setNavigationState`.
  - Connections: Provides appointment data and operations to schedule views and appointment forms.
  - State: useState for appointments, patients, and navigationState with localStorage persistence.

- **ConsultationContext**: 
  - Purpose: Handles consultation types and their configurations.
  - Key Exports: `newConsultationType`, `setNewConsultationType`, `addConsultationType`, `removeConsultationType`.
  - Connections: Depends on DoctorContext for editedConfig to update consultationTypes.
  - State: useState for newConsultationType.

- **CoverageContext**: 
  - Purpose: Manages medical insurance coverage plans and doctor exceptions.
  - Key Exports: `medicalCoverages`, `toggleDoctorCoverageException`, `isDoctorCoverageExcluded`.
  - Connections: Used by DoctorCoveragesTab and DoctorConsultationTypesTab for copays/exclusions.
  - State: useState for medicalCoverages and exceptions.

- **DoctorContext**: 
  - Purpose: Handles doctor data, selection, dialog state, and CRUD operations.
  - Key Exports: `doctors`, `selectedDoctor`, `setSelectedDoctor`, `isDialogOpen`, `setIsDialogOpen`, `editedConfig`, `setEditedConfig`, `addDoctor`, `editDoctor`, `removeDoctor`, `handleSaveConfig`.
  - Connections: Uses MedicalCenterContext for activeMedicalCenterId and updates medical center's doctor list on doctor addition/removal. Uses AppStateContext for setActiveConfigTab and useDoctors hook for CRUD operations.
  - State: useState for doctors, selectedDoctor, isDialogOpen, and editedConfig with localStorage persistence through the useDoctors hook.

- **MedicalCenterContext**: 
  - Purpose: Manages medical centers data and active center selection.
  - Key Exports: `medicalCenters`, `setMedicalCenters`, `activeMedicalCenterId`, `setActiveMedicalCenterId`, `updateMedicalCenter`.
  - Connections: Provides activeMedicalCenterId to useDoctors (via DoctorContext) to load/save doctors for the active center.
  - State: useState for medicalCenters and activeMedicalCenterId with localStorage persistence.

- **NewDoctorContext**: 
  - Purpose: Manages specialty editing for new doctor creation.
  - Key Exports: `newSpecialty`, `setNewSpecialty`, `addSpecialty`, `removeSpecialty`.
  - Connections: Depends on DoctorContext for editedConfig in DoctorInfoTab.
  - State: useState for newSpecialty.

- **ScheduleContext**: 
  - Purpose: Manages doctor schedules (working days, hours).
  - Key Exports: `handleToggleWorkingDay`, `handleUpdateWorkingHours`, `handleAddWorkingHours`, `handleRemoveWorkingHours`, `getWorkingDaysCount`.
  - Connections: Depends on DoctorContext for editedConfig to update workingDays.
  - State: None directly; relies on DoctorContext for state.

### Hooks

- **useDoctors**: 
  - Purpose: Custom hook for doctor CRUD operations with persistence.
  - Key Exports: `doctors`, `loading`, `addDoctor`, `editDoctor`, `removeDoctor`, `refreshDoctors`.
  - Connections: Uses storage service for saving/retrieving doctors and constants for initializing workingDays.
  - State: useState for doctors with useEffect for loading from storage based on the active medical center.
  - Details: Initializes new doctors with defaults for workingDays, appointmentSlotDuration, etc.

### Data Management

- **initialData.ts**:
  - Purpose: Centralized source of initial data for the application.
  - Key Exports: `initialDoctors`, `initialMedicalCenters`.
  - Connections: Used by storage service for application initialization.
  - Details: Provides consistent initial state across the application and fallback data when localStorage is not available.

- **consultationTypes.ts**:
  - Purpose: Predefined consultation type templates.
  - Key Exports: `CONSULTATION_TYPES`.
  - Connections: Used by initialData.ts to create doctor consultation types.
  - Details: Contains base templates that can be customized per doctor.

### Services

- **storage.ts**: 
  - Purpose: Local storage service for persisting application state.
  - Key Exports: `getMedicalCenters`, `saveMedicalCenters`, `getActiveMedicalCenterId`, `saveActiveMedicalCenterId`, `getDoctors`, `saveDoctors`, `getAllDoctorsInSystem`, `initializeStorage`, `getDoctorIdMap`, `registerDoctorId`, `getUnassignedDoctors`.
  - Connections: Used by contexts and hooks for data persistence.
  - Details: Implements consistent storage key management, fallbacks to initial data, doctor ID mapping, and handles localStorage unavailability.

- **storageEvents.ts**:
  - Purpose: Enables real-time updates across browser tabs.
  - Key Exports: `subscribeToStorageEvent`, `unsubscribeFromStorageEvent`, `triggerStorageEvent`, `initStorageEvents`, `StorageEventType`.
  - Connections: Used by context providers to listen for changes from other tabs.
  - Details: Implements a pub/sub pattern for storage events with type-safe event handling.

## Key Functionality

### Doctor Management

- Create, update, and delete doctor profiles
- Configure specialties, credentials, and contact information
- Set doctor-specific appointment durations and booking rules

### Schedule Configuration

- Define working days and hours
- Configure date-specific exceptions (holidays, special hours)
- Set booking advance notice requirements
- Configure appointment slot durations

### Appointment Handling

- Book appointments for patients
- Manage appointment status (scheduled, confirmed, canceled, completed)
- Handle appointment rescheduling
- Track appointment history

### Medical Center Management

- Configure medical center details
- Manage doctors associated with each center
- Handle center-specific settings

### Medical Insurance Integration

- Configure insurance providers and plans
- Set up co-payment amounts
- Define excluded coverages by plan or provider

### Consultation Types

- Define consultation types (regular, urgent, follow-up, etc.)
- Configure consultation-specific durations
- Set pricing and coverage options by consultation type
- Configure online booking availability

## Application Data Flow

The application follows a hierarchical data flow with localStorage persistence and real-time updates:

1. **Initialization**:
   - On application start, `storage.initializeStorage()` ensures localStorage has initial data
   - `initStorageEvents()` sets up listeners for storage events from other tabs
   - Context providers load data from localStorage or fall back to initial data

2. **Medical Center Selection**:
   - User selects a medical center
   - `MedicalCenterContext` updates `activeMedicalCenterId` in state and localStorage
   - Storage event is triggered to notify other tabs of the change
   - `DoctorContext` detects the change and loads doctors for that center through `useDoctors`

3. **Doctor Operations**:
   - When a doctor is added/edited/removed, the changes are:
     - Updated in `DoctorContext` state
     - Persisted to localStorage via `storage.saveDoctors()`
     - Storage event is triggered to notify other tabs
     - If adding/removing a doctor, the medical center's doctor list is also updated
     - Doctor's MN is registered in the doctor ID map for consistent reference

4. **Medical Center Updates**:
   - When a medical center is updated:
     - Changes are applied to `MedicalCenterContext` state
     - Persisted to localStorage via `storage.saveMedicalCenters()`
     - Storage event is triggered to notify other tabs

5. **Appointment Operations**:
   - When appointments are added/edited/removed:
     - Changes are applied to `AppointmentContext` state
     - Persisted to localStorage with debouncing
     - Storage event is triggered to notify other tabs

6. **Real-Time Synchronization**:
   - When storage changes in one tab, other tabs receive storage events
   - Context providers update their state based on the new data
   - UI automatically re-renders with the latest data
   - Debouncing and update tracking prevent circular updates

7. **Data Persistence**:
   - All operations on doctors, medical centers, and appointments are automatically persisted
   - All data is stored with prefixed keys to avoid conflicts
   - Each medical center's doctors are stored separately for efficiency
   - Doctor identity is maintained across medical centers via ID mapping

This architecture ensures data remains consistent across page refreshes and browser sessions while maintaining a clear separation of concerns.

The Five Most Important Questions About This Codebase
How is data persisted in the application?
The application uses localStorage for data persistence
Data is managed through a storage service (services/storage.ts) that provides consistent access patterns
The application uses a three-tier data management architecture: Initial Data Layer, Storage Service Layer, and Context Layer
Real-time synchronization across browser tabs is implemented using storage events
There's no server-side persistence yet (mentioned as a future enhancement)
What is the state management architecture?
The application uses React Context API for state management
Multiple context providers handle different aspects of the application (MedicalCenter, Doctor, Appointment, etc.)
Contexts are hierarchical with MedicalCenterContext at the top level
Each context subscribes to storage events to maintain synchronization across tabs
Debounced updates are used to prevent race conditions and excessive storage operations
How are medical centers, doctors, and appointments related?
Medical centers contain references to doctors (array of doctor IDs)
Doctors can belong to multiple medical centers
Appointments are linked to both doctors and medical centers
The active medical center ID is used to filter and display relevant doctors
Doctors have complex scheduling rules including working days, exceptions, and consultation types
What are the key data models and their relationships?
MedicalCenter: Contains basic info and references to doctors
Doctor: Contains personal info, specialties, working schedule, and consultation types
Appointment: Links patients, doctors, and medical centers with scheduling information
Patient: Contains personal and insurance information
ConsultationType: Defines appointment types with duration, pricing, and insurance rules
User: Defines system users with roles and permissions
How is the application structured and what technologies does it use?
Built with Next.js 15 and TypeScript
Uses a combination of custom UI components with Radix UI primitives and MUI components
Styled with Tailwind CSS
Follows a directory structure with app/, components/, contexts/, data/, hooks/, services/, types/, and utils/
Uses a freemium monetization model with reminder packs and new patient fees
Implements a double-sided marketplace connecting healthcare providers with patients

## Deployment

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables in `.env.local`
4. Run the development server:
   ```
   npm run dev
   ```
5. Build for production:
   ```
   npm run build
   ```
6. Start the production server:
   ```
   npm start
   ```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Submit a pull request

## License

[License information]

---

© 2024 Turnera - Medical Scheduling Platform
