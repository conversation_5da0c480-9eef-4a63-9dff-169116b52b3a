'use client';

import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { CABA_LOCATIONS, GBA_LOCATIONS } from '@/data/locations';

// Interface matching the CreateMedicalCenterRequest from the image
interface CreateMedicalCenterRequest {
    mail: string;
    phone: string;
    imageUrl?: string;
    name: string;
    address: string;
    postalCode: number;
    province: string;
    city: string;
    acceptsSelfPaidPatients: boolean;
    floorNumber: number | null;
    departmentNumber: string;
    latitude: number;
    longitude: number;
}

// Interface for the response
interface CreateMedicalCenterResponse {
    id: number;
}

interface NewMedicalCenterDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: (medicalCenterId: number) => void;
}

export default function NewMedicalCenterDialog({ 
    isOpen, 
    onClose, 
    onSuccess 
}: NewMedicalCenterDialogProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [isGeocoding, setIsGeocoding] = useState(false);
    const [formData, setFormData] = useState<CreateMedicalCenterRequest>({
        mail: '',
        phone: '',
        imageUrl: '',
        name: '',
        address: '',
        postalCode: 0,
        province: '',
        city: '',
        acceptsSelfPaidPatients: true,
        floorNumber: null,
        departmentNumber: '',
        latitude: 0,
        longitude: 0,
    });

    // Map refs
    const mapContainerRef = useRef<HTMLDivElement | null>(null);
    const mapInstanceRef = useRef<L.Map | null>(null);
    const markerRef = useRef<L.Marker | null>(null);

    // Shared styles aligned with platform/landing pages
    const inputClassNames =
        'rounded-xl border-[#e5e9f2] bg-[#f7f8fb] text-[#2d2f46] placeholder:text-slate-400 shadow-inner focus:border-blue-400 focus:ring-2 focus:ring-blue-100';
    const labelClassNames = 'text-slate-700 text-sm font-medium';

    const handleInputChange = (field: keyof CreateMedicalCenterRequest, value: string | number | boolean | null) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Geocode full address and update map pin
    const locateOnMap = async () => {
        const provinceName = formData.province === 'caba' ? 'Ciudad Autónoma de Buenos Aires' : formData.province === 'gba' ? 'Buenos Aires' : '';
        const parts = [formData.address, formData.city, provinceName, 'Argentina'].filter(Boolean);
        const fullAddress = parts.join(', ');

        if (!fullAddress || parts.length < 2) {
            toast.error('Completá dirección y ciudad/provincia para ubicar en el mapa');
            return;
        }

        setIsGeocoding(true);
        try {
            const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(fullAddress)}&accept-language=es,es-AR&addressdetails=1&countrycodes=ar`;
            const res = await fetch(url, { headers: { 'Accept': 'application/json' } });
            const data = await res.json();
            if (Array.isArray(data) && data.length > 0) {
                const lat = parseFloat(data[0].lat);
                const lon = parseFloat(data[0].lon);
                setFormData(prev => ({ ...prev, latitude: lat, longitude: lon }));
                if (markerRef.current && mapInstanceRef.current) {
                    markerRef.current.setLatLng([lat, lon]);
                    mapInstanceRef.current.setView([lat, lon], 16);
                }
                toast.success('Ubicación encontrada');
            } else {
                toast.error('No pudimos encontrar esa dirección');
            }
        } catch (e) {
            console.error('Geocoding error', e);
            toast.error('Error buscando la dirección');
        } finally {
            setIsGeocoding(false);
        }
    };

    const validateForm = (): boolean => {
        const requiredFields: (keyof CreateMedicalCenterRequest)[] = [
            'mail', 'phone', 'name', 'address', 'postalCode', 
            'province', 'city', 'latitude', 'longitude'
        ];

        for (const field of requiredFields) {
            if (!formData[field] || 
                (typeof formData[field] === 'string' && formData[field] === '') ||
                (typeof formData[field] === 'number' && formData[field] === 0)) {
                toast.error(`El campo ${getFieldLabel(field)} es requerido`);
                return false;
            }
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.mail)) {
            toast.error('El formato del email no es válido');
            return false;
        }

        // Validate postal code is positive
        if (formData.postalCode <= 0) {
            toast.error('El código postal debe ser un número positivo');
            return false;
        }

        return true;
    };

    const getFieldLabel = (field: keyof CreateMedicalCenterRequest): string => {
        const labels: Record<keyof CreateMedicalCenterRequest, string> = {
            mail: 'Email',
            phone: 'Teléfono',
            imageUrl: 'URL de imagen',
            name: 'Nombre',
            address: 'Dirección',
            postalCode: 'Código postal',
            province: 'Provincia',
            city: 'Ciudad',
            acceptsSelfPaidPatients: 'Acepta pacientes particulares',
            floorNumber: 'Número de piso',
            departmentNumber: 'Número de departamento',
            latitude: 'Latitud',
            longitude: 'Longitud'
        };
        return labels[field];
    };

    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            // Call our local API route to create the medical center
            const response = await fetch('/api/medical-centers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to create medical center');
            }

            const result = await response.json();
            
            toast.success('Establecimiento médico creado exitosamente');
            
            // Call the success callback with the new ID
            if (onSuccess && result.id) {
                onSuccess(typeof result.id === 'string' ? parseInt(result.id) : result.id);
            }
            
            // Reset form and close dialog
            setFormData({
                mail: '',
                phone: '',
                imageUrl: '',
                name: '',
                address: '',
                postalCode: 0,
                province: '',
                city: '',
                acceptsSelfPaidPatients: true,
                floorNumber: null,
                departmentNumber: '',
                latitude: 0,
                longitude: 0,
            });
            
            onClose();
        } catch (error) {
            console.error('Error creating medical center:', error);
            toast.error(error instanceof Error ? error.message : 'Error al crear el establecimiento médico');
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        if (!isLoading) {
            setFormData({
                mail: '',
                phone: '',
                imageUrl: '',
                name: '',
                address: '',
                postalCode: 0,
                province: '',
                city: '',
                acceptsSelfPaidPatients: true,
                floorNumber: null,
                departmentNumber: '',
                latitude: 0,
                longitude: 0,
            });
            onClose();
        }
    };

    // Initialize map when dialog opens
    useEffect(() => {
        if (!isOpen) {
            // Cleanup on close
            if (mapInstanceRef.current) {
                try { mapInstanceRef.current.remove(); } catch {}
            }
            mapInstanceRef.current = null;
            markerRef.current = null;
            if (mapContainerRef.current) {
                mapContainerRef.current.innerHTML = '';
            }
            return;
        }

        // Delay to ensure content is mounted
        const timeout = setTimeout(() => {
            if (!mapContainerRef.current) return;

            // Default to Buenos Aires center if no coords
            const lat = formData.latitude || -34.6037;
            const lng = formData.longitude || -58.3816;

            // Cleanup existing
            if (mapInstanceRef.current) {
                try { mapInstanceRef.current.remove(); } catch {}
                mapInstanceRef.current = null;
                markerRef.current = null;
            }

            const map = L.map(mapContainerRef.current, { zoomControl: false, attributionControl: false }).setView([lat, lng], 14);
            mapInstanceRef.current = map;

            L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                subdomains: 'abcd',
                maxZoom: 19
            }).addTo(map);

            L.control.zoom({ position: 'bottomright' }).addTo(map);
            // Ensure Leaflet recalculates size inside dialog
            try { setTimeout(() => { map.invalidateSize?.(); }, 0); } catch {}

            // Use the same white medical center icon style as SearchResultsMap
            // Inline CSS to ensure visibility in this dialog (classes are defined in SearchResultsMap only)
            const customIcon = L.divIcon({
                className: 'custom-map-marker',
                html: `
                  <div class="marker-container" style="display:flex;align-items:center;justify-content:center;">
                    <div class="pin-dot" style="width:22px;height:22px;border-radius:50%;background:#0070F3;box-shadow:0 0 0 3px #ffffff inset, 0 1px 3px rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;">
                      <div class="pin-center-dot" style="width:6px;height:6px;border-radius:50%;background:#ffffff;"></div>
                    </div>
                  </div>
                `,
                iconSize: [22, 22],
                iconAnchor: [11, 11],
                popupAnchor: [0, -11]
            });

            const marker = L.marker([lat, lng], { draggable: true, icon: customIcon }).addTo(map);
            markerRef.current = marker;

            const updateFromLatLng = (latlng: L.LatLng) => {
                setFormData(prev => ({ ...prev, latitude: latlng.lat, longitude: latlng.lng }));
            };

            map.on('click', (e: L.LeafletMouseEvent) => {
                marker.setLatLng(e.latlng);
                updateFromLatLng(e.latlng);
            });

            marker.on('dragend', () => {
                const pos = marker.getLatLng();
                updateFromLatLng(pos);
            });
        }, 50);

        return () => clearTimeout(timeout);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    // Keep marker synced when coords change from other sources
    useEffect(() => {
        if (mapInstanceRef.current && markerRef.current) {
            const lat = formData.latitude || -34.6037;
            const lng = formData.longitude || -58.3816;
            markerRef.current.setLatLng([lat, lng]);
            mapInstanceRef.current.setView([lat, lng]);
        }
    }, [formData.latitude, formData.longitude]);

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="w-[92vw] max-w-7xl max-h-[90vh] overflow-y-auto p-6 md:p-8 rounded-2xl border border-slate-200 shadow-2xl shadow-slate-400/20 bg-white">
                <DialogHeader>
                    <DialogTitle className="text-[1.35rem] font-semibold text-slate-700">
                        Crear Nuevo Establecimiento Médico
                    </DialogTitle>
                </DialogHeader>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Left Column: All Forms */}
                    <div className="space-y-8">
                        {/* Basic Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-700">Información Básica</h3>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className={labelClassNames}>Nombre *</Label>
                                    <Input
                                        id="name"
                                        className={inputClassNames}
                                        value={formData.name}
                                        onChange={(e) => handleInputChange('name', e.target.value)}
                                        placeholder="Nombre del establecimiento"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="mail" className={labelClassNames}>Email *</Label>
                                    <Input
                                        id="mail"
                                        type="email"
                                        className={inputClassNames}
                                        value={formData.mail}
                                        onChange={(e) => handleInputChange('mail', e.target.value)}
                                        placeholder="<EMAIL>"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="phone" className={labelClassNames}>Teléfono *</Label>
                                    <Input
                                        id="phone"
                                        className={inputClassNames}
                                        value={formData.phone}
                                        onChange={(e) => handleInputChange('phone', e.target.value)}
                                        placeholder="+54 11 1234-5678"
                                    />
                                </div>

                            </div>
                        </div>

                        {/* Location Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-700">Datos de ubicación</h3>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="address" className={labelClassNames}>Dirección *</Label>
                                    <Input
                                        id="address"
                                        className={inputClassNames}
                                        value={formData.address}
                                        onChange={(e) => handleInputChange('address', e.target.value)}
                                        placeholder="Calle y número"
                                    />
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label className={labelClassNames}>Provincia *</Label>
                                        <Select
                                            value={formData.province}
                                            onValueChange={(val) => {
                                                setFormData(prev => ({ ...prev, province: val, city: '' }));
                                            }}
                                        >
                                            <SelectTrigger className={inputClassNames}>
                                                <SelectValue placeholder="Seleccioná provincia (CABA / GBA)" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="caba">Capital Federal (CABA)</SelectItem>
                                                <SelectItem value="gba">Gran Buenos Aires (GBA)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <Label className={labelClassNames}>Ciudad *</Label>
                                        <Select
                                            value={formData.city}
                                            onValueChange={(val) => setFormData(prev => ({ ...prev, city: val }))}
                                            disabled={!formData.province}
                                        >
                                            <SelectTrigger className={inputClassNames}>
                                                <SelectValue placeholder={formData.province ? 'Seleccioná ciudad' : 'Elegí provincia primero'} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {(formData.province === 'caba' ? CABA_LOCATIONS : formData.province === 'gba' ? GBA_LOCATIONS : []).map(loc => (
                                                    <SelectItem key={loc.id} value={loc.name}>{loc.name}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                                <div className="flex items-end gap-4">
                                    <div className="space-y-2 flex-1">
                                        <Label htmlFor="postalCode" className={labelClassNames}>Código Postal *</Label>
                                        <Input
                                            id="postalCode"
                                            type="number"
                                            className={inputClassNames}
                                            value={formData.postalCode || ''}
                                            onChange={(e) => handleInputChange('postalCode', parseInt(e.target.value) || 0)}
                                            placeholder="1234"
                                        />
                                    </div>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={locateOnMap}
                                        disabled={isGeocoding}
                                        className="rounded-xl text-blue-600 border-blue-300 hover:border-blue-600 hover:bg-blue-50 h-10"
                                    >
                                        {isGeocoding ? 'Buscando...' : 'Ubicar en el mapa'}
                                    </Button>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="floorNumber" className={labelClassNames}>Número de Piso</Label>
                                        <Input
                                            id="floorNumber"
                                            type="number"
                                            className={inputClassNames}
                                            value={formData.floorNumber || ''}
                                            onChange={(e) => handleInputChange('floorNumber', e.target.value ? parseInt(e.target.value) : null)}
                                            placeholder="1"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="departmentNumber" className={labelClassNames}>Número de Departamento</Label>
                                        <Input
                                            id="departmentNumber"
                                            className={inputClassNames}
                                            value={formData.departmentNumber}
                                            onChange={(e) => handleInputChange('departmentNumber', e.target.value)}
                                            placeholder="A"
                                        />
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>

                                         {/* Right Column: Map and Buttons */}
                     <div className="flex flex-col h-full">
                         <div className="space-y-4 flex-1">
                             <h3 className="text-lg font-semibold text-slate-700">Ubicación en el mapa</h3>
                             <div className="text-sm text-slate-700 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                                 ¿No es la dirección correcta? Podés ajustar la dirección en el mapa.
                             </div>
                             <div className="rounded-xl overflow-hidden border border-slate-200">
                                 <div ref={mapContainerRef} className="h-[500px] w-full" />
                             </div>
                         </div>
                         
                         {/* Action Buttons */}
                         <div className="flex justify-end space-x-3 pt-4">
                             <Button
                                 variant="outline"
                                 onClick={handleClose}
                                 disabled={isLoading}
                                 className="rounded-xl text-blue-600 border-blue-300 hover:border-blue-600 hover:bg-blue-50"
                             >
                                 Cancelar
                             </Button>
                             <Button
                                 onClick={handleSubmit}
                                 disabled={isLoading}
                                 className="rounded-xl bg-blue-600 hover:bg-blue-700"
                             >
                                 {isLoading ? 'Creando...' : 'Crear Establecimiento'}
                             </Button>
                         </div>
                     </div>
                 </div>
            </DialogContent>
        </Dialog>
    );
}
