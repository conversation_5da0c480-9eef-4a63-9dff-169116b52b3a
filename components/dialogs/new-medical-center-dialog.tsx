'use client';

import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, Check, MapPin, User, FileText } from 'lucide-react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { CABA_LOCATIONS, GBA_LOCATIONS } from '@/data/locations';

// Interface matching the CreateMedicalCenterRequest from the image
interface CreateMedicalCenterRequest {
    mail: string;
    phone: string;
    imageUrl?: string;
    name: string;
    address: string;
    postalCode: number;
    province: string;
    city: string;
    acceptsSelfPaidPatients: boolean;
    floorNumber: number | null;
    departmentNumber: string;
    latitude: number;
    longitude: number;
}

// Interface for the response
interface CreateMedicalCenterResponse {
    id: number;
}

interface NewMedicalCenterDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: (medicalCenterId: number) => void;
}

type Step = 1 | 2 | 3;

export default function NewMedicalCenterDialog({
    isOpen,
    onClose,
    onSuccess
}: NewMedicalCenterDialogProps) {
    const [currentStep, setCurrentStep] = useState<Step>(1);
    const [isLoading, setIsLoading] = useState(false);
    const [isGeocoding, setIsGeocoding] = useState(false);
    const [formData, setFormData] = useState<CreateMedicalCenterRequest>({
        mail: '',
        phone: '',
        imageUrl: '',
        name: '',
        address: '',
        postalCode: 0,
        province: '',
        city: '',
        acceptsSelfPaidPatients: true, // System-set, not user input
        floorNumber: null,
        departmentNumber: '',
        latitude: 0,
        longitude: 0,
    });

    // Map refs
    const mapContainerRef = useRef<HTMLDivElement | null>(null);
    const mapInstanceRef = useRef<L.Map | null>(null);
    const markerRef = useRef<L.Marker | null>(null);

    // Shared styles aligned with platform/landing pages
    const inputClassNames =
        'rounded-xl border-[#e5e9f2] bg-[#f7f8fb] text-[#2d2f46] placeholder:text-slate-400 shadow-inner focus:border-blue-400 focus:ring-2 focus:ring-blue-100';
    const labelClassNames = 'text-slate-700 text-sm font-medium';

    const handleInputChange = (field: keyof CreateMedicalCenterRequest, value: string | number | boolean | null) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Geocode full address and update map pin
    const locateOnMap = async () => {
        const provinceName = formData.province === 'caba' ? 'Ciudad Autónoma de Buenos Aires' : formData.province === 'gba' ? 'Buenos Aires' : '';
        const parts = [formData.address, formData.city, provinceName, 'Argentina'].filter(Boolean);
        const fullAddress = parts.join(', ');

        if (!fullAddress || parts.length < 2) {
            toast.error('Completá dirección y ciudad/provincia para ubicar en el mapa');
            return;
        }

        setIsGeocoding(true);
        try {
            const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(fullAddress)}&accept-language=es,es-AR&addressdetails=1&countrycodes=ar`;
            const res = await fetch(url, { headers: { 'Accept': 'application/json' } });
            const data = await res.json();
            if (Array.isArray(data) && data.length > 0) {
                const lat = parseFloat(data[0].lat);
                const lon = parseFloat(data[0].lon);
                setFormData(prev => ({ ...prev, latitude: lat, longitude: lon }));
                if (markerRef.current && mapInstanceRef.current) {
                    markerRef.current.setLatLng([lat, lon]);
                    mapInstanceRef.current.setView([lat, lon], 16);
                }
                toast.success('Ubicación encontrada');
            } else {
                toast.error('No pudimos encontrar esa dirección');
            }
        } catch (e) {
            console.error('Geocoding error', e);
            toast.error('Error buscando la dirección');
        } finally {
            setIsGeocoding(false);
        }
    };


    const handleSubmit = async () => {
        if (!validateStep(3)) {
            return;
        }

        setIsLoading(true);

        try {
            // Call our local API route to create the medical center
            const response = await fetch('/api/medical-centers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to create medical center');
            }

            const result = await response.json();
            
            toast.success('Establecimiento médico creado exitosamente');
            
            // Call the success callback with the new ID
            if (onSuccess && result.id) {
                onSuccess(typeof result.id === 'string' ? parseInt(result.id) : result.id);
            }
            
            // Reset form and close dialog
            setCurrentStep(1);
            setFormData({
                mail: '',
                phone: '',
                imageUrl: '',
                name: '',
                address: '',
                postalCode: 0,
                province: '',
                city: '',
                acceptsSelfPaidPatients: true,
                floorNumber: null,
                departmentNumber: '',
                latitude: 0,
                longitude: 0,
            });

            onClose();
        } catch (error) {
            console.error('Error creating medical center:', error);
            toast.error(error instanceof Error ? error.message : 'Error al crear el establecimiento médico');
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        if (!isLoading) {
            setCurrentStep(1);
            setFormData({
                mail: '',
                phone: '',
                imageUrl: '',
                name: '',
                address: '',
                postalCode: 0,
                province: '',
                city: '',
                acceptsSelfPaidPatients: true,
                floorNumber: null,
                departmentNumber: '',
                latitude: 0,
                longitude: 0,
            });
            onClose();
        }
    };

    const validateStep = (step: Step): boolean => {
        switch (step) {
            case 1:
                // Basic info validation
                if (!formData.name.trim()) {
                    toast.error('El nombre es requerido');
                    return false;
                }
                if (!formData.mail.trim()) {
                    toast.error('El email es requerido');
                    return false;
                }
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(formData.mail)) {
                    toast.error('El formato del email no es válido');
                    return false;
                }
                if (!formData.phone.trim()) {
                    toast.error('El teléfono es requerido');
                    return false;
                }
                return true;

            case 2:
                // Location validation
                if (!formData.address.trim()) {
                    toast.error('La dirección es requerida');
                    return false;
                }
                if (!formData.province) {
                    toast.error('La provincia es requerida');
                    return false;
                }
                if (!formData.city) {
                    toast.error('La ciudad es requerida');
                    return false;
                }
                if (!formData.postalCode || formData.postalCode <= 0) {
                    toast.error('El código postal es requerido');
                    return false;
                }
                if (!formData.latitude || !formData.longitude) {
                    toast.error('Por favor, ubica el establecimiento en el mapa');
                    return false;
                }
                return true;

            case 3:
                // Final validation (all fields)
                return validateStep(1) && validateStep(2);

            default:
                return false;
        }
    };

    const handleNext = () => {
        if (validateStep(currentStep)) {
            setCurrentStep((prev) => Math.min(prev + 1, 3) as Step);
        }
    };

    const handlePrevious = () => {
        setCurrentStep((prev) => Math.max(prev - 1, 1) as Step);
    };

    // Initialize map when dialog opens
    useEffect(() => {
        if (!isOpen) {
            // Cleanup on close
            if (mapInstanceRef.current) {
                try { mapInstanceRef.current.remove(); } catch {}
            }
            mapInstanceRef.current = null;
            markerRef.current = null;
            if (mapContainerRef.current) {
                mapContainerRef.current.innerHTML = '';
            }
            return;
        }

        // Delay to ensure content is mounted
        const timeout = setTimeout(() => {
            if (!mapContainerRef.current) return;

            // Default to Buenos Aires center if no coords
            const lat = formData.latitude || -34.6037;
            const lng = formData.longitude || -58.3816;

            // Cleanup existing
            if (mapInstanceRef.current) {
                try { mapInstanceRef.current.remove(); } catch {}
                mapInstanceRef.current = null;
                markerRef.current = null;
            }

            const map = L.map(mapContainerRef.current, { zoomControl: false, attributionControl: false }).setView([lat, lng], 14);
            mapInstanceRef.current = map;

            L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                subdomains: 'abcd',
                maxZoom: 19
            }).addTo(map);

            L.control.zoom({ position: 'bottomright' }).addTo(map);
            // Ensure Leaflet recalculates size inside dialog
            try { setTimeout(() => { map.invalidateSize?.(); }, 0); } catch {}

            // Use the same white medical center icon style as SearchResultsMap
            // Inline CSS to ensure visibility in this dialog (classes are defined in SearchResultsMap only)
            const customIcon = L.divIcon({
                className: 'custom-map-marker',
                html: `
                  <div class="marker-container" style="display:flex;align-items:center;justify-content:center;">
                    <div class="pin-dot" style="width:22px;height:22px;border-radius:50%;background:#0070F3;box-shadow:0 0 0 3px #ffffff inset, 0 1px 3px rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;">
                      <div class="pin-center-dot" style="width:6px;height:6px;border-radius:50%;background:#ffffff;"></div>
                    </div>
                  </div>
                `,
                iconSize: [22, 22],
                iconAnchor: [11, 11],
                popupAnchor: [0, -11]
            });

            const marker = L.marker([lat, lng], { draggable: true, icon: customIcon }).addTo(map);
            markerRef.current = marker;

            const updateFromLatLng = (latlng: L.LatLng) => {
                setFormData(prev => ({ ...prev, latitude: latlng.lat, longitude: latlng.lng }));
            };

            map.on('click', (e: L.LeafletMouseEvent) => {
                marker.setLatLng(e.latlng);
                updateFromLatLng(e.latlng);
            });

            marker.on('dragend', () => {
                const pos = marker.getLatLng();
                updateFromLatLng(pos);
            });
        }, 50);

        return () => clearTimeout(timeout);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    // Keep marker synced when coords change from other sources
    useEffect(() => {
        if (mapInstanceRef.current && markerRef.current) {
            const lat = formData.latitude || -34.6037;
            const lng = formData.longitude || -58.3816;
            markerRef.current.setLatLng([lat, lng]);
            mapInstanceRef.current.setView([lat, lng]);
        }
    }, [formData.latitude, formData.longitude]);

    // Step Components
    const renderStep1 = () => (
        <div className="space-y-6">
            <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                    <User className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-slate-700">Información Básica</h3>
                <p className="text-slate-500">Empecemos con los datos principales del establecimiento</p>
            </div>

            <div className="space-y-4 max-w-md mx-auto">
                <div className="space-y-2">
                    <Label htmlFor="name" className={labelClassNames}>Nombre del establecimiento *</Label>
                    <Input
                        id="name"
                        className={inputClassNames}
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Hospital San Juan"
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="mail" className={labelClassNames}>Email de contacto *</Label>
                    <Input
                        id="mail"
                        type="email"
                        className={inputClassNames}
                        value={formData.mail}
                        onChange={(e) => handleInputChange('mail', e.target.value)}
                        placeholder="<EMAIL>"
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="phone" className={labelClassNames}>Teléfono *</Label>
                    <Input
                        id="phone"
                        className={inputClassNames}
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+54 11 1234-5678"
                    />
                </div>
            </div>
        </div>
    );

    const renderStep2 = () => (
        <div className="space-y-6">
            <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                    <MapPin className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-slate-700">Ubicación</h3>
                <p className="text-slate-500">¿Dónde se encuentra el establecimiento?</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column: Location Form */}
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="address" className={labelClassNames}>Dirección *</Label>
                        <Input
                            id="address"
                            className={inputClassNames}
                            value={formData.address}
                            onChange={(e) => handleInputChange('address', e.target.value)}
                            placeholder="Av. Corrientes 1234"
                        />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label className={labelClassNames}>Provincia *</Label>
                            <Select
                                value={formData.province}
                                onValueChange={(val) => {
                                    setFormData(prev => ({ ...prev, province: val, city: '' }));
                                }}
                            >
                                <SelectTrigger className={inputClassNames}>
                                    <SelectValue placeholder="Seleccionar" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="caba">Capital Federal</SelectItem>
                                    <SelectItem value="gba">Gran Buenos Aires</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label className={labelClassNames}>Ciudad *</Label>
                            <Select
                                value={formData.city}
                                onValueChange={(val) => setFormData(prev => ({ ...prev, city: val }))}
                                disabled={!formData.province}
                            >
                                <SelectTrigger className={inputClassNames}>
                                    <SelectValue placeholder={formData.province ? 'Seleccionar' : 'Elegí provincia'} />
                                </SelectTrigger>
                                <SelectContent>
                                    {(formData.province === 'caba' ? CABA_LOCATIONS : formData.province === 'gba' ? GBA_LOCATIONS : []).map(loc => (
                                        <SelectItem key={loc.id} value={loc.name}>{loc.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="flex items-end gap-4">
                        <div className="space-y-2 flex-1">
                            <Label htmlFor="postalCode" className={labelClassNames}>Código Postal *</Label>
                            <Input
                                id="postalCode"
                                type="number"
                                className={inputClassNames}
                                value={formData.postalCode || ''}
                                onChange={(e) => handleInputChange('postalCode', parseInt(e.target.value) || 0)}
                                placeholder="1234"
                            />
                        </div>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={locateOnMap}
                            disabled={isGeocoding}
                            className="rounded-xl text-blue-600 border-blue-300 hover:border-blue-600 hover:bg-blue-50 h-10"
                        >
                            {isGeocoding ? 'Buscando...' : 'Ubicar'}
                        </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="floorNumber" className={labelClassNames}>Piso</Label>
                            <Input
                                id="floorNumber"
                                type="number"
                                className={inputClassNames}
                                value={formData.floorNumber || ''}
                                onChange={(e) => handleInputChange('floorNumber', e.target.value ? parseInt(e.target.value) : null)}
                                placeholder="1"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="departmentNumber" className={labelClassNames}>Departamento</Label>
                            <Input
                                id="departmentNumber"
                                className={inputClassNames}
                                value={formData.departmentNumber}
                                onChange={(e) => handleInputChange('departmentNumber', e.target.value)}
                                placeholder="A"
                            />
                        </div>
                    </div>
                </div>

                {/* Right Column: Map */}
                <div className="space-y-4">
                    <div className="text-sm text-slate-600 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                        Ajustá la ubicación en el mapa si es necesario
                    </div>
                    <div className="rounded-xl overflow-hidden border border-slate-200">
                        <div ref={mapContainerRef} className="h-[400px] w-full" />
                    </div>
                </div>
            </div>
        </div>
    );

    const renderStep3 = () => (
        <div className="space-y-6">
            <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <Check className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-slate-700">Confirmar Información</h3>
                <p className="text-slate-500">Revisá que todo esté correcto antes de crear el establecimiento</p>
            </div>

            <div className="max-w-2xl mx-auto space-y-6">
                {/* Basic Info Summary */}
                <div className="bg-slate-50 rounded-xl p-4 space-y-3">
                    <h4 className="font-semibold text-slate-700 flex items-center gap-2">
                        <User className="w-4 h-4" />
                        Información Básica
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div>
                            <span className="text-slate-500">Nombre:</span>
                            <p className="font-medium">{formData.name}</p>
                        </div>
                        <div>
                            <span className="text-slate-500">Email:</span>
                            <p className="font-medium">{formData.mail}</p>
                        </div>
                        <div>
                            <span className="text-slate-500">Teléfono:</span>
                            <p className="font-medium">{formData.phone}</p>
                        </div>
                    </div>
                </div>

                {/* Location Summary */}
                <div className="bg-slate-50 rounded-xl p-4 space-y-3">
                    <h4 className="font-semibold text-slate-700 flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        Ubicación
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div>
                            <span className="text-slate-500">Dirección:</span>
                            <p className="font-medium">{formData.address}</p>
                        </div>
                        <div>
                            <span className="text-slate-500">Ciudad:</span>
                            <p className="font-medium">{formData.city}, {formData.province === 'caba' ? 'CABA' : 'GBA'}</p>
                        </div>
                        <div>
                            <span className="text-slate-500">Código Postal:</span>
                            <p className="font-medium">{formData.postalCode}</p>
                        </div>
                        {(formData.floorNumber || formData.departmentNumber) && (
                            <div>
                                <span className="text-slate-500">Piso/Depto:</span>
                                <p className="font-medium">
                                    {formData.floorNumber ? `Piso ${formData.floorNumber}` : ''}
                                    {formData.floorNumber && formData.departmentNumber ? ', ' : ''}
                                    {formData.departmentNumber ? `Depto ${formData.departmentNumber}` : ''}
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );

    const getStepTitle = () => {
        switch (currentStep) {
            case 1: return 'Crear Nuevo Establecimiento Médico';
            case 2: return 'Ubicación del Establecimiento';
            case 3: return 'Confirmar y Crear';
            default: return 'Crear Nuevo Establecimiento Médico';
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="w-[92vw] max-w-4xl max-h-[90vh] overflow-y-auto p-6 md:p-8 rounded-2xl border border-slate-200 shadow-2xl shadow-slate-400/20 bg-white">
                <DialogHeader>
                    <DialogTitle className="text-[1.35rem] font-semibold text-slate-700">
                        {getStepTitle()}
                    </DialogTitle>

                    {/* Step Progress Indicator */}
                    <div className="flex items-center justify-center space-x-4 mt-4">
                        {[1, 2, 3].map((step) => (
                            <div key={step} className="flex items-center">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                                    step === currentStep
                                        ? 'bg-blue-600 text-white'
                                        : step < currentStep
                                            ? 'bg-green-600 text-white'
                                            : 'bg-slate-200 text-slate-500'
                                }`}>
                                    {step < currentStep ? <Check className="w-4 h-4" /> : step}
                                </div>
                                {step < 3 && (
                                    <div className={`w-12 h-0.5 mx-2 ${
                                        step < currentStep ? 'bg-green-600' : 'bg-slate-200'
                                    }`} />
                                )}
                            </div>
                        ))}
                    </div>
                </DialogHeader>

                {/* Step Content */}
                <div className="py-6">
                    {currentStep === 1 && renderStep1()}
                    {currentStep === 2 && renderStep2()}
                    {currentStep === 3 && renderStep3()}
                </div>

                {/* Navigation Buttons */}
                <div className="flex justify-between items-center pt-6 border-t border-slate-200">
                    <div>
                        {currentStep > 1 && (
                            <Button
                                variant="outline"
                                onClick={handlePrevious}
                                disabled={isLoading}
                                className="rounded-xl text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50"
                            >
                                <ChevronLeft className="w-4 h-4 mr-2" />
                                Anterior
                            </Button>
                        )}
                    </div>

                    <div className="flex space-x-3">
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            disabled={isLoading}
                            className="rounded-xl text-blue-600 border-blue-300 hover:border-blue-600 hover:bg-blue-50"
                        >
                            Cancelar
                        </Button>

                        {currentStep < 3 ? (
                            <Button
                                onClick={handleNext}
                                disabled={isLoading}
                                className="rounded-xl bg-blue-600 hover:bg-blue-700"
                            >
                                Siguiente
                                <ChevronRight className="w-4 h-4 ml-2" />
                            </Button>
                        ) : (
                            <Button
                                onClick={handleSubmit}
                                disabled={isLoading}
                                className="rounded-xl bg-green-600 hover:bg-green-700"
                            >
                                {isLoading ? 'Creando...' : 'Crear Establecimiento'}
                                <Check className="w-4 h-4 ml-2" />
                            </Button>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
