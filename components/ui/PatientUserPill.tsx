"use client"

import { useState, useRef, useEffect } from "react"
import { User, LogOut, Calendar, ChevronDown, Building, Stethoscope } from "lucide-react"
import Link from "next/link"

import type { User as UserType } from "@/types/users"
import { UserRole } from "@/types/users"
import type { Patient } from "@/types/patient"
import { Button } from "@/components/ui/button"
import { getProfessionalUserId } from "@/utils/userUtils"
import { useAuth } from "@/contexts/AuthContext"

export interface PatientUserPillProps {
  currentUser: UserType | null;
  currentPatient: Patient | null;
  logout: () => void;
  variant?: 'light' | 'dark'; // New prop to handle different backgrounds
}

export function PatientUserPill({
  currentUser,
  currentPatient,
  logout,
  variant = 'light'
}: PatientUserPillProps) {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  const { loginWithAuth0 } = useAuth()

  // Click outside handler for user menu
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // If user is not authenticated, show login button
  if (!currentUser) {
    return (
      <Button
        variant="outline"
        size="sm"
        className={`
          relative overflow-hidden transition-all duration-200 ease-in-out
          font-medium shadow-sm hover:shadow-md
          ${variant === 'light' 
            ? 'bg-white text-blue-700 border-blue-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-800' 
            : 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:border-blue-700'
          }
          group
        `}
        onClick={() => {
          try {
            const currentPath = window.location.pathname + window.location.search
            window.localStorage.setItem('loginRedirectUrl', currentPath)
          } catch (err) {
            console.error('PatientUserPill - Error storing redirect URL:', err)
          }
          loginWithAuth0()
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
        <User className="mr-2 h-4 w-4 transition-transform group-hover:scale-110" />
        <span className="relative">Iniciar sesión</span>
      </Button>
    )
  }

  // If user is authenticated, show user pill with dropdown
  return (
    <div className="relative" ref={userMenuRef}>
      <button
        className={`
          group relative flex items-center gap-2 pl-2 pr-3 py-1.5 rounded-full transition-all duration-200 ease-in-out
          font-medium text-[0.9rem] shadow-sm hover:shadow-md
          bg-white text-gray-800 border border-blue-200 hover:border-blue-300 hover:bg-gray-50
          ${variant === 'dark'
            ? 'md:bg-blue-50 md:text-blue-700 md:border md:border-blue-200 md:hover:bg-blue-100 md:hover:border-blue-300'
            : 'border-blue-200 hover:border-blue-300 md:bg-white md:text-gray-800 md:border md:border-blue-200 md:hover:bg-gray-50 md:hover:border-blue-300'
          }
          ${isUserMenuOpen ? 'ring-2 ring-blue-500/20 border-blue-300' : ''}
        `}
        onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
      >
        {/* User Avatar */}
        <div className="w-7 h-7 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold text-[0.9rem] shadow-sm ring-1 ring-white group-hover:ring-blue-100 transition-all duration-200">
          {currentPatient ? currentPatient.name.charAt(0).toUpperCase() : currentUser.name.charAt(0).toUpperCase()}
        </div>

        {/* User Name */}
        <span className="font-medium max-w-[100px] truncate">
          {currentPatient ? currentPatient.name.split(' ')[0] : currentUser.name.split(' ')[0]}
        </span>

        {/* Dropdown Arrow */}
        <ChevronDown className={`h-4 w-4 text-gray-500 transition-all duration-200 ${isUserMenuOpen ? 'rotate-180 text-blue-600' : 'group-hover:text-gray-700'}`} />
      </button>

      {/* Dropdown Menu */}
      {isUserMenuOpen && (
        <div className="absolute top-full right-0 mt-1.5 w-60 z-50 animate-in fade-in-0 zoom-in-95 duration-200">
          <div className="bg-white rounded-xl shadow-xl border border-gray-200/80 backdrop-blur-sm overflow-hidden">
            {/* User Info Header */}
            <div className="px-3 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
              <div className="flex items-center gap-2.5">
                <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold shadow-sm">
                  {currentPatient ? currentPatient.name.charAt(0).toUpperCase() : currentUser.name.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-semibold text-gray-900 truncate text-[13px]">
                    {currentPatient ? currentPatient.name : currentUser.name}
                  </p>
                </div>
              </div>
            </div>

            {/* Menu Items - separated by sections per role */}
            <div className="py-1.5">
              {/* Paciente section */}
              {currentUser?.roles.includes(UserRole.TURNERA_USER) && (
                <div className="py-1.5">
                  <div className="px-3 pb-0.5 text-[11px] font-semibold text-gray-500 uppercase tracking-wide">Paciente</div>
                  <Link
                    href="/plataforma/paciente"
                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                    onClick={() => setIsUserMenuOpen(false)}
                  >
                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                      <Calendar className="h-3.5 w-3.5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Mis turnos</p>
                      <p className="text-[11px] text-gray-500">Ver próximos turnos</p>
                    </div>
                  </Link>
                  <Link
                    href="/plataforma/paciente?editProfile=true"
                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                    onClick={() => setIsUserMenuOpen(false)}
                  >
                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                      <User className="h-3.5 w-3.5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Mi perfil</p>
                      <p className="text-[11px] text-gray-500">Configurar datos</p>
                    </div>
                  </Link>
                </div>
              )}

              {/* Profesional section */}
              {currentUser?.roles.includes(UserRole.PROFESSIONAL_USER) && (
                <div className="py-1.5 border-t border-gray-100">
                  <div className="px-3 pb-0.5 text-[11px] font-semibold text-gray-500 uppercase tracking-wide">Profesional</div>
                  <Link
                    href={`/plataforma/profesional/${getProfessionalUserId(currentUser)}`}
                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                    onClick={() => setIsUserMenuOpen(false)}
                  >
                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                      <Stethoscope className="h-3.5 w-3.5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Ir a mi agenda profesional</p>
                      <p className="text-[11px] text-gray-500">Ver agenda médica</p>
                    </div>
                  </Link>
                </div>
              )}

              {/* Administrador section */}
              {currentUser?.roles.includes(UserRole.EMPLOYEE_USER) && (
                <div className="py-1.5 border-t border-gray-100">
                  <div className="px-3 pb-0.5 text-[11px] font-semibold text-gray-500 uppercase tracking-wide">Administrador</div>
                  <Link
                    href="/plataforma/establecimiento"
                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                    onClick={() => setIsUserMenuOpen(false)}
                  >
                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                      <Building className="h-3.5 w-3.5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Ir a mi establecimiento</p>
                      <p className="text-[11px] text-gray-500">Ver mis centros de salud</p>
                    </div>
                  </Link>
                </div>
              )}
            </div>

            {/* Logout Section */}
            <div className="border-t border-gray-100 py-1.5">
              <button
                className="flex w-full items-center px-3 py-2 text-[0.9rem] text-red-600 hover:bg-red-50 transition-all duration-150 group"
                onClick={() => {
                  logout();
                  setIsUserMenuOpen(false);
                }}
              >
                <div className="w-7 h-7 rounded-md bg-red-100 flex items-center justify-center mr-2.5 group-hover:bg-red-200 transition-colors">
                  <LogOut className="h-3.5 w-3.5 text-red-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">Cerrar sesión</p>
                  <p className="text-[11px] text-red-500/70">Salir de la cuenta</p>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
