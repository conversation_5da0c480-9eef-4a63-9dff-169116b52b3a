"use client"

import {useEffect, useRef, useState} from "react"
import {Building, Calendar, Check, ChevronDown, ChevronLeft, ChevronRight, LogOut, Share2, Stethoscope, User} from "lucide-react"
import Link from "next/link"

import type {User as UserType} from "@/types/users"
import {UserRole} from "@/types/users"
import {Button} from "@/components/ui/button"
import {Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip"
import {MedicalCenterRoleForEmployeeUser} from "@/types/MedicalCenter/medicalCenterRoleForEmployeeUser"
import {getProfessionalUserId} from "@/utils/userUtils"

interface MedicalCenterUserPillProps {
    medicalCenter: MedicalCenterRoleForEmployeeUser | undefined;
    currentUser: UserType | null;
    medicalCenterId: number;
    logout: () => void;
    variant?: 'light' | 'dark';
    showShare?: boolean;
    dropUp?: boolean;
}

export function MedicalCenterUserPill({
                                          medicalCenter,
                                          currentUser,
                                          medicalCenterId,
                                          logout,
                                          variant = 'light',
                                          showShare = true,
                                          dropUp = false
                                      }: MedicalCenterUserPillProps) {
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
    const [isCopied, setIsCopied] = useState(false)
    const userMenuRef = useRef<HTMLDivElement>(null)


    // Click outside handler for user menu
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setIsUserMenuOpen(false)
            }
        }

        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    // Function to copy the medical center booking URL to clipboard
    const copyMedicalCenterBookingUrl = () => {
        const url = `${window.location.origin}/plataforma/reservar/cm/${medicalCenterId}`
        navigator.clipboard.writeText(url)
            .then(() => {
                setIsCopied(true)
                setTimeout(() => setIsCopied(false), 2000)
            })
            .catch(err => console.error('Failed to copy URL: ', err))
    }

    return (
        <div className="flex items-center gap-3">
            {/* Share Medical Center Icon - Cohesive style (toggleable) */}
            {showShare && (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="outline"
                                size="sm"
                                className={`
                                    relative overflow-hidden transition-all duration-200 ease-in-out
                                    p-0 h-9 w-9 rounded-full shadow-sm hover:shadow-md
                                    ${variant === 'light'
                                        ? 'bg-white text-blue-700 border-blue-300/50 hover:bg-blue-50 hover:border-blue-400 hover:text-blue-900'
                                        : 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:border-blue-700'
                                    }
                                    group
                                `}
                                onClick={copyMedicalCenterBookingUrl}
                                aria-label="Copiar enlace de reserva del centro"
                            >
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                                {isCopied ? <Check className="h-4 w-4 transition-transform group-hover:scale-110" /> : <Share2 className="h-4 w-4 transition-transform group-hover:scale-110" />}
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                            <p>{isCopied ? "¡URL copiada!" : "Copiar URL de reserva para pacientes"}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            )}

            {/* User Pill - Matching PatientUserPill style */}
            <div className="relative" ref={userMenuRef}>
                <button
                    className={`
                        group relative flex items-center gap-2 pl-2 pr-3 py-1.5 rounded-full transition-all duration-200 ease-in-out
                        font-medium text-[0.9rem] shadow-sm hover:shadow-md
                        bg-white text-gray-800 border border-blue-300/50 hover:border-blue-400 hover:bg-blue-50/50
                        ${variant === 'dark'
                            ? 'md:bg-blue-50 md:text-blue-700 md:border md:border-blue-200 md:hover:bg-blue-100 md:hover:border-blue-300'
                            : ''
                        }
                        ${isUserMenuOpen ? 'ring-2 ring-blue-500/20 border-blue-400' : ''}
                    `}
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                >
                    {/* User Avatar */}
                    <div className="w-7 h-7 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold text-[0.9rem] shadow-sm ring-1 ring-white group-hover:ring-blue-100 transition-all duration-200">
                        {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                    </div>

                    {/* User Name */}
                    <span className="font-medium max-w-[100px] truncate">
                        {currentUser?.name ? currentUser.name.split(' ')[0] : 'Usuario'}
                    </span>

                    {/* Dropdown Arrow */}
                    {dropUp ? (
                        isUserMenuOpen ? (
                            <ChevronLeft className="h-4 w-4 text-blue-600 transition-all duration-200" />
                        ) : (
                            <ChevronRight className="h-4 w-4 text-gray-500 group-hover:text-gray-700 transition-all duration-200" />
                        )
                    ) : (
                        <ChevronDown className={`h-4 w-4 text-gray-500 transition-all duration-200 ${isUserMenuOpen ? 'rotate-180 text-blue-600' : 'group-hover:text-gray-700'}`} />
                    )}
                </button>

                {/* Dropdown Menu */}
                {isUserMenuOpen && (
                    <div className={`absolute ${dropUp ? 'left-full ml-2 bottom-0 origin-bottom-left' : 'right-0 top-full mt-1.5 origin-top-right'} w-60 z-50 animate-in fade-in-0 zoom-in-95 duration-200`}>
                        <div className="bg-white rounded-xl shadow-xl border border-gray-200/80 backdrop-blur-sm overflow-hidden">
                            {/* User Info Header */}
                            <div className="px-3 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                                <div className="flex items-center gap-2.5">
                                    <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold shadow-sm">
                                        {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="font-semibold text-gray-900 truncate text-[13px]">
                                            {currentUser?.name || 'Usuario'}
                                        </p>
                                        <p className="text-[11px] text-gray-500 truncate">
                                            {medicalCenter?.name}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Menu Items - separated by sections per role */}
                            <div className="py-1.5">
                              {/* Paciente section */}
                              {currentUser?.roles.includes(UserRole.TURNERA_USER) && (
                                <div className="py-1.5">
                                  <div className="px-3 pb-0.5 text-[11px] font-semibold text-gray-500 uppercase tracking-wide">Paciente</div>
                                  <Link
                                    href="/plataforma/paciente"
                                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                                    onClick={() => setIsUserMenuOpen(false)}
                                  >
                                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                                      <Calendar className="h-3.5 w-3.5 text-blue-600" />
                                    </div>
                                    <div className="flex-1">
                                      <p className="font-medium">Mis turnos</p>
                                      <p className="text-[11px] text-gray-500">Ver próximos turnos</p>
                                    </div>
                                  </Link>
                                  <Link
                                    href="/plataforma/paciente?editProfile=true"
                                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                                    onClick={() => setIsUserMenuOpen(false)}
                                  >
                                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                                      <User className="h-3.5 w-3.5 text-blue-600" />
                                    </div>
                                    <div className="flex-1">
                                      <p className="font-medium">Mi perfil</p>
                                      <p className="text-[11px] text-gray-500">Configurar datos</p>
                                    </div>
                                  </Link>
                                </div>
                              )}

                              {/* Profesional section */}
                              {currentUser?.roles.includes(UserRole.PROFESSIONAL_USER) && (
                                <div className="py-1.5 border-t border-gray-100">
                                  <div className="px-3 pb-0.5 text-[11px] font-semibold text-gray-500 uppercase tracking-wide">Profesional</div>
                                  <Link
                                    href={`/plataforma/profesional/${getProfessionalUserId(currentUser)}`}
                                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                                    onClick={() => setIsUserMenuOpen(false)}
                                  >
                                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                                      <Stethoscope className="h-3.5 w-3.5 text-blue-600" />
                                    </div>
                                    <div className="flex-1">
                                      <p className="font-medium">Ir a mi agenda profesional</p>
                                      <p className="text-[11px] text-gray-500">Ver agenda médica</p>
                                    </div>
                                  </Link>
                                </div>
                              )}

                              {/* Administrador section */}
                              {currentUser?.roles.includes(UserRole.EMPLOYEE_USER) && (
                                <div className="py-1.5 border-t border-gray-100">
                                  <div className="px-3 pb-0.5 text-[11px] font-semibold text-gray-500 uppercase tracking-wide">Administrador</div>
                                  <Link
                                    href="/plataforma/establecimiento"
                                    className="flex items-center px-3 py-2 text-[0.9rem] text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150 group"
                                    onClick={() => setIsUserMenuOpen(false)}
                                  >
                                    <div className="w-7 h-7 rounded-md bg-blue-100 flex items-center justify-center mr-2.5 group-hover:bg-blue-200 transition-colors">
                                      <Building className="h-3.5 w-3.5 text-blue-600" />
                                    </div>
                                    <div className="flex-1">
                                      <p className="font-medium">Ir a mi establecimiento</p>
                                      <p className="text-[11px] text-gray-500">Ver mis centros de salud</p>
                                    </div>
                                  </Link>
                                </div>
                              )}
                            </div>

                            {/* Logout Section */}
                            <div className="border-t border-gray-100 py-1.5">
                                <button
                                    className="flex w-full items-center px-3 py-2 text-[0.9rem] text-red-600 hover:bg-red-50 transition-all duration-150 group"
                                    onClick={() => {
                                        logout();
                                        setIsUserMenuOpen(false);
                                    }}
                                >
                                    <div className="w-7 h-7 rounded-md bg-red-100 flex items-center justify-center mr-2.5 group-hover:bg-red-200 transition-colors">
                                        <LogOut className="h-3.5 w-3.5 text-red-600" />
                                    </div>
                                    <div className="flex-1">
                                        <p className="font-medium">Cerrar sesión</p>
                                        <p className="text-[11px] text-red-500/70">Salir de la cuenta</p>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}
