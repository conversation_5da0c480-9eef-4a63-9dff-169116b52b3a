"use client"

import { Search } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import Image from "next/image"

export default function MedicalCenterPageSkeleton() {
  return (
    <div className="medical-center-page-skeleton">
      <style jsx global>{`
        html {
          font-size: 13px;
        }
        @media (min-width: 1500px) {
          html {
            font-size: 15px;
          }
        }
        @media (min-width: 2560px) {
          html {
            font-size: 17px;
          }
        }
        body {
          background: #ffffff;
          position: relative;
        }
        .card-grid-item { opacity: 1; }
      `}</style>

      <div className="min-h-screen bg-white">
        <div className="w-full grid grid-cols-[18rem_1fr] gap-0">
          {/* Left Sidebar */}
          <aside className="hidden md:flex flex-col sticky top-0 h-screen bg-white px-5 py-2.5 border-r border-[#e5e9f2]">
            <div className="flex items-center gap-2 px-2 pb-2">
              <Image src="/images/turnera-logo.svg" alt="Turnera" width={120} height={32} className="h-7 w-auto align-middle relative top-[4.5px]" />
            </div>
            <div className="px-2 py-3 flex items-center justify-between gap-2">
              <div className="h-5 w-40 bg-slate-200 rounded animate-pulse" />
              <div className="h-8 w-8 rounded-full border border-blue-200 bg-blue-50" />
            </div>
            <nav className="mt-2 space-y-2 flex-1 px-2">
              <div className="h-9 w-full rounded-lg bg-slate-100 border border-slate-200 animate-pulse" />
              <div className="h-9 w-full rounded-lg bg-slate-100 border border-slate-200 animate-pulse" />
              <div className="h-9 w-full rounded-lg bg-slate-100 border border-slate-200 animate-pulse" />
            </nav>
            <div className="mt-auto px-2 pt-3 pb-1 border-t border-[#e5e9f2]">
              <div className="h-9 w-full rounded-lg bg-slate-100 border border-slate-200 animate-pulse" />
            </div>
          </aside>

          {/* Right Content */}
          <section className="min-w-0">
            {/* Top bar */}
            <div className="grid grid-cols-[1fr_minmax(480px,900px)_auto] items-center bg-white px-12 py-2 gap-3">
              <div />
              <div className="w-full col-start-2 justify-self-center max-w-md xl:max-w-lg">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-slate-300 h-5 w-5" />
                  <Input
                    type="text"
                    className="w-full pl-12 pr-4 h-10 rounded-full border-[#e5e9f2] bg-[#f7f8fb] text-[#2d2f46] placeholder:text-slate-400 shadow-inner"
                    placeholder=""
                    disabled
                  />
                </div>
              </div>
              <div className="justify-self-end">
                <div className="flex items-center gap-2 pl-2 pr-3 py-1.5 rounded-full bg-white border border-slate-200 shadow-sm">
                  <div className="w-7 h-7 rounded-full bg-slate-200 animate-pulse" />
                  <div className="h-4 w-28 bg-slate-200 rounded animate-pulse" />
                  <div className="h-3 w-3 bg-slate-200 rounded-sm" />
                </div>
              </div>
            </div>

            {/* Main content */}
            <div className="bg-gradient-to-b from-[#f3f8fc] to-[#f4f8fa] border border-[#e5e9f2] shadow-inner rounded-tl-2xl min-h-[calc(100vh-3.5rem)] pt-12 pb-6 pl-12 pr-6">
              <div className="w-full">

                {/* Title + segmented controls */}
                <div className="flex items-center gap-3">
                  <div className="h-6 w-48 bg-slate-200 rounded animate-pulse" />
                  <div className="h-7 w-7 rounded-full bg-blue-50 border border-blue-200" />
                </div>
                <div className="mt-3 inline-flex rounded-lg border border-slate-200 bg-white p-1 shadow-sm gap-1">
                  <Button className="px-4 py-2 rounded-md text-[0.95rem] bg-slate-100 text-transparent" disabled>
                    Día
                  </Button>
                  <Button className="px-4 py-2 rounded-md text-[0.95rem] bg-slate-100 text-transparent" disabled>
                    Semana
                  </Button>
                  <Button className="px-4 py-2 rounded-md text-[0.95rem] bg-slate-100 text-transparent" disabled>
                    Mes
                  </Button>
                </div>

                {/* Separator */}
                <div className="mt-8 mb-4 h-px w-full bg-[#e5e9f2]" />

                {/* Professionals header */}
                <div className="mt-10">
                  <div className="flex items-center gap-3 flex-wrap">
                    <div className="h-6 w-40 bg-slate-200 rounded animate-pulse" />
                    <div className="h-8 w-28 rounded-lg border border-slate-200 bg-white shadow-sm" />
                  </div>

                  {/* Professionals grid */}
                  <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-6">
                    {Array.from({ length: 8 }).map((_, index) => (
                      <div key={index} className="group relative overflow-hidden border rounded-2xl transition-all duration-300 min-h-[12rem] flex flex-col bg-white">
                        <div className="p-4 bg-gradient-to-br from-blue-50 via-blue-100 to-sky-100">
                          <div className="flex items-center gap-4">
                            <div className="h-10 w-10 rounded-full ring-2 ring-white/40 bg-blue-200" />
                            <div className="min-w-0 flex-1">
                              <div className="h-4 w-40 bg-blue-200 rounded mb-2" />
                              <div className="h-3 w-56 bg-blue-100 rounded" />
                            </div>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span className="inline-flex h-2.5 w-2.5 rounded-full bg-blue-200" />
                              <span className="h-4 w-48 bg-blue-100 rounded" />
                            </div>
                            <span className="rounded-full bg-slate-100 text-transparent text-xs font-semibold px-2 py-0.5">
                              00 turnos
                            </span>
                          </div>
                        </div>
                        <div className="px-4 pb-4 flex items-center justify-between mt-auto">
                          <div className="flex items-center gap-2 text-blue-700">
                            <div className="h-4 w-4 bg-blue-200 rounded" />
                            <span className="h-4 w-20 bg-blue-100 rounded" />
                          </div>
                          <div className="h-3 w-16 bg-blue-100 rounded" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
}
