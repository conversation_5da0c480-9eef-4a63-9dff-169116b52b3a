"use client"

import {use<PERSON>ontext, useEffect, useRef, useState} from "react"
import {NewDoctorContext} from "@/contexts/NewDoctorContext"
import {DoctorContext} from "@/contexts/DoctorContext"
import {Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/components/ui/dialog"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Button} from "@/components/ui/button"
import {Badge} from "@/components/ui/badge"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Plus, Search, X} from "lucide-react"
import {SPECIALTIES, Specialty} from "@/data/specialties"
import {Doctor} from "@/types/doctor"
import {sendDoctorInvitationEmail} from "@/services/email"
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import {PatientContext} from "@/contexts/PatientContext"
import {defaultDoc<PERSON><PERSON><PERSON><PERSON>s, User, User<PERSON><PERSON>} from "@/types/users"
import {generateUserId} from "@/utils/idGenerator"
import {toast} from "sonner"
import {PhoneInput} from "react-international-phone"
import "react-international-phone/style.css"
import "@/styles/phone-input.css"
import {getSpanishCountries} from "@/data/phoneCountries"
import {PhoneNumberUtil} from 'google-libphonenumber'

// Initialize phone number utility
const phoneUtil = PhoneNumberUtil.getInstance()

// Helper function to capitalize names properly
const capitalizeName = (name: string) => {
    return name
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(" ")
}

export default function NewDoctorDialog() {
    const {
        isNewDoctorDialogOpen,
        setIsNewDoctorDialogOpen,
        newDoctor,
        setNewDoctor,
        removeSpecialty,
    } = useContext(NewDoctorContext)

    const {addDoctor, setDoctors} = useContext(DoctorContext)
    const {activeMedicalCenterId} = useContext(MedicalCenterContext)
    const patientContext = useContext(PatientContext)

    const [mnSearched, setMnSearched] = useState(false)
    const [searchResult, setSearchResult] = useState<"found" | "not-found" | "duplicate" | null>(null)
    const [showSpecialtyDropdown, setShowSpecialtyDropdown] = useState(false)
    const [selectedSpecialties, setSelectedSpecialties] = useState<Specialty[]>([])
    const [isProcessing, setIsProcessing] = useState(false)
    const [phoneError, setPhoneError] = useState("")
    const specialtyRef = useRef<HTMLDivElement>(null)

    const handleSearchByMN = () => {
        if (!activeMedicalCenterId || !newDoctor.mn) {
            return;
        }

        // Find this doctor's ID by MN if they exist in the system
        const existingDoctorId = storage.findExistingDoctorId(newDoctor.mn);
        console.log(`Doctor search by MN: ${newDoctor.mn}, found ID: ${existingDoctorId || 'none'}`);

        // Check if this doctor exists anywhere in the system
        if (existingDoctorId) {
            // Doctor exists in the system - get all medical centers to check assignments
            const allMedicalCenters = storage.getMedicalCenters();

            // Check if this doctor is assigned to the current medical center
            const currentMedicalCenter = allMedicalCenters.find(mc => mc.id === activeMedicalCenterId);

            // First validate the medical center exists
            if (!currentMedicalCenter) {
                console.error(`Could not find medical center with ID: ${activeMedicalCenterId}`);
                setSearchResult("not-found");
                setMnSearched(true);
                return;
            }

            // Perform a more thorough check for assignment by checking both:
            // 1. If the doctor ID is in the medical center's doctors array
            const isListedInCenter = currentMedicalCenter.doctors.includes(existingDoctorId);

            // 2. If the doctor actually exists in the center's storage
            const centerDoctors = storage.getDoctors(activeMedicalCenterId);
            const existsInStorage = centerDoctors.some(d => d.id === existingDoctorId);

            // Only consider as assigned if the doctor is both listed AND exists in storage
            let isAssignedToCurrentCenter = isListedInCenter && existsInStorage;

            console.log(`Doctor ${existingDoctorId} assignment check for ${activeMedicalCenterId}:`, {
                isListedInCenter,
                existsInStorage,
                isAssignedToCurrentCenter
            });

            // If the doctor is listed in the center but not in storage, we need to repair this inconsistency
            if (isListedInCenter && !existsInStorage) {
                console.warn(`Inconsistency detected: Doctor ${existingDoctorId} is listed in medical center ${activeMedicalCenterId} but not found in storage`);

                // Get the doctor from any other center in the system
                const allDoctorsInSystem = storage.getAllDoctorsInSystem();
                const doctorToRepair = allDoctorsInSystem.find(d => d.id === existingDoctorId);

                // We'll let the doctor be added to fix the inconsistency
                if (doctorToRepair) {
                    console.log(`Found doctor data to repair inconsistency: ${doctorToRepair.name}`);
                    isAssignedToCurrentCenter = false;
                }
            }

            // Check which other centers this doctor belongs to
            const centersWithDoctor = allMedicalCenters.filter(mc => mc.doctors.includes(existingDoctorId));
            console.log(`Doctor ${existingDoctorId} is assigned to centers:`, centersWithDoctor.map(mc => mc.id));

            // Get the doctor's full data from any center in the system
            const allDoctorsInSystem = storage.getAllDoctorsInSystem();
            const existingDoctor = allDoctorsInSystem.find(d => d.id === existingDoctorId);

            if (!existingDoctor) {
                console.error(`Doctor ID ${existingDoctorId} found in MN map but no doctor data found`);
                setSearchResult("not-found");
                setMnSearched(true);
                return;
            }

            if (isAssignedToCurrentCenter) {
                // Already in this medical center - can't add again
                console.log(`Doctor ${existingDoctorId} is already assigned to medical center ${activeMedicalCenterId}`);
                setSearchResult("duplicate");

                // Split the existing doctor name into first and last name
                const fullName = existingDoctor.name.replace(/^(Dr\.|Dra\.)\s/, "").trim();
                const nameParts = fullName.split(" ");
                const firstName = nameParts[0] || "";
                const lastName = nameParts.slice(1).join(" ") || "";

                setNewDoctor({
                    ...newDoctor,
                    firstName: capitalizeName(firstName),
                    lastName: capitalizeName(lastName),
                    specialties: existingDoctor.specialties, // Keep existing specialties for duplicate case
                    title: existingDoctor.name.startsWith("Dr.") ? "Dr" : "Dra",
                    email: existingDoctor.email || "",
                    phone: existingDoctor.phone || "",
                    dni: existingDoctor.dni || "",
                });
            } else {
                // Doctor exists in the system but not in this medical center - can add
                console.log(`Doctor ${existingDoctorId} exists in the system but not in medical center ${activeMedicalCenterId}`);
                setSearchResult("found");

                // Split the existing doctor name into first and last name
                const fullName = existingDoctor.name.replace(/^(Dr\.|Dra\.)\s/, "").trim();
                const nameParts = fullName.split(" ");
                const firstName = nameParts[0] || "";
                const lastName = nameParts.slice(1).join(" ") || "";

                setNewDoctor({
                    ...newDoctor,
                    firstName: capitalizeName(firstName),
                    lastName: capitalizeName(lastName),
                    specialties: [], // Start with empty specialties - user must select which ones apply to this medical center
                    title: existingDoctor.name.startsWith("Dr.") ? "Dr" : "Dra",
                    email: existingDoctor.email || "",
                    phone: existingDoctor.phone || "",
                    dni: existingDoctor.dni || "",
                });
                console.log(`Found existing doctor with ID ${existingDoctor.id} in other medical center`);
            }
        } else {
            // New doctor that doesn't exist anywhere
            console.log(`No doctor found with MN ${newDoctor.mn} - creating new`);
            setNewDoctor({
                ...newDoctor,
                firstName: "",
                lastName: "",
                specialties: [] as Specialty[],
                title: "Dr",
                email: "",
                phone: "",
                dni: "",
            });
            setSearchResult("not-found");
        }

        setMnSearched(true);
    }

    const handleMNChange = (value: string) => {
        setNewDoctor({...newDoctor, mn: value})
        setMnSearched(false)
        setSearchResult(null)
        setShowSpecialtyDropdown(false)
        setSelectedSpecialties([])
    }

    useEffect(() => {
        if (isNewDoctorDialogOpen) {
            // Reset all dialog state when opening
            setNewDoctor({
                firstName: "",
                lastName: "",
                mn: "",
                specialties: [] as Specialty[],
                title: "Dr",
                email: "",
                phone: "",
                dni: ""
            })
            setMnSearched(false)
            setSearchResult(null)
            setShowSpecialtyDropdown(false)
            setSelectedSpecialties([])
            setIsProcessing(false)
            setPhoneError("")
        }
    }, [isNewDoctorDialogOpen, setNewDoctor])

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (specialtyRef.current && !specialtyRef.current.contains(e.target as Node)) {
                setShowSpecialtyDropdown(false)
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    const handleSpecialtySelect = (specialty: Specialty) => {
        setSelectedSpecialties((prev) =>
            prev.includes(specialty) ? prev.filter((s) => s !== specialty) : [...prev, specialty]
        )
    }

    const confirmSpecialties = () => {
        const updatedSpecialties = [...new Set([...newDoctor.specialties, ...selectedSpecialties])]
        setNewDoctor({...newDoctor, specialties: updatedSpecialties})
        setShowSpecialtyDropdown(false)
        setSelectedSpecialties([])
    }

    if (!newDoctor) {
        return null
    }

    const isDuplicate = searchResult === "duplicate"

    // When creating a new doctor, initialize both schedule formats:
    const defaultWorkingDays = {
        "0": {enabled: false, hours: []},
        "1": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
        "2": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
        "3": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
        "4": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
        "5": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
        "6": {enabled: false, hours: []},
    };

    const handleCreateNewDoctor = async () => {
        if (!activeMedicalCenterId || isProcessing) {
            return;
        }

        setIsProcessing(true);
        console.log("Processing doctor creation/addition...");

        try {
            // Make sure the active medical center exists
            const allMedicalCenters = storage.getMedicalCenters();
            const activeMedicalCenter = allMedicalCenters.find(mc => mc.id === activeMedicalCenterId);

            if (!activeMedicalCenter) {
                console.error(`Medical center ${activeMedicalCenterId} not found, cannot add doctor`);
                setIsProcessing(false);
                return;
            }

            // Check if a doctor with this MN already exists in the system
            const existingDoctorId = storage.findExistingDoctorId(newDoctor.mn);

            if (existingDoctorId) {
                console.log(`Using existing doctor ID ${existingDoctorId} instead of creating a new one`);

                // Perform a thorough check if the doctor is already in the medical center
                const isListedInCenter = activeMedicalCenter.doctors.includes(existingDoctorId);
                const centerDoctors = storage.getDoctors(activeMedicalCenterId);
                const existsInStorage = centerDoctors.some(d => d.id === existingDoctorId);

                console.log(`Doctor ${existingDoctorId} assignment check for ${activeMedicalCenterId}:`, {
                    isListedInCenter,
                    existsInStorage
                });

                // Consider a doctor assigned only if both conditions are true
                if (isListedInCenter && existsInStorage) {
                    console.warn(`Doctor ${existingDoctorId} is already properly assigned to medical center ${activeMedicalCenterId}`);
                    setIsProcessing(false);
                    return;
                }

                // Even if the doctor is listed but not in storage, we should proceed to fix this inconsistency

                // Get existing doctor data from any medical center
                const allDoctors = storage.getAllDoctorsInSystem();
                const existingDoctor = allDoctors.find(d => d.id === existingDoctorId);

                if (!existingDoctor) {
                    console.error(`Could not find doctor data for ID ${existingDoctorId}`);
                    // Close dialog since we can't proceed
                    setIsNewDoctorDialogOpen(false);
                    setIsProcessing(false);
                    return;
                }

                // Add the existing doctor to this medical center
                const didAdd = storage.addDoctorToMedicalCenter(existingDoctorId, activeMedicalCenterId);

                if (didAdd) {
                    console.log(`Added existing doctor ${existingDoctorId} to medical center ${activeMedicalCenterId}`);

                    // Create a copy of the existing doctor with updated specialties for this medical center
                    const doctorForThisCenter = {
                        ...existingDoctor,
                        specialties: newDoctor.specialties as Specialty[] // Use the specialties modified in the dialog
                    };

                    // Add to this center's doctors in storage
                    const centerDoctors = storage.getDoctors(activeMedicalCenterId);

                    // Check if doctor already exists, and if so, update their specialties
                    const existingDoctorIndex = centerDoctors.findIndex(d => d.id === existingDoctorId);
                    let updatedDoctors;

                    if (existingDoctorIndex >= 0) {
                        // Update existing doctor with new specialties
                        updatedDoctors = [...centerDoctors];
                        updatedDoctors[existingDoctorIndex] = doctorForThisCenter;
                        console.log(`Updated existing doctor ${existingDoctorId} with new specialties for medical center ${activeMedicalCenterId}`);
                    } else {
                        // Add new doctor to center
                        updatedDoctors = [...centerDoctors, doctorForThisCenter];
                        console.log(`Added new doctor ${existingDoctorId} to medical center ${activeMedicalCenterId}`);
                    }

                    storage.saveDoctors(activeMedicalCenterId, updatedDoctors);

                    // Update context directly
                    setDoctors(updatedDoctors);

                    console.log(`Doctor specialties saved for ${existingDoctorId}:`, newDoctor.specialties);
                }

                // Validate storage to ensure consistency
                storage.validateStorage();

                // Dispatch events and refresh page like in DoctorDialog
                if (typeof window !== 'undefined') {
                    // Event for config saved
                    const savedEvent = new CustomEvent('doctor-config-saved', {
                        detail: {
                            doctorId: existingDoctorId,
                            medicalCenterId: activeMedicalCenterId,
                            timestamp: Date.now()
                        }
                    });
                    window.dispatchEvent(savedEvent);
                    console.log(`NewDoctorDialog: Dispatched doctor-config-saved event for ${existingDoctorId}`);

                    // Show the loading overlay immediately
                    const loadingEvent = new CustomEvent('show-loading-overlay');
                    window.dispatchEvent(loadingEvent);

                    // Trigger a hard refresh after a short delay to allow events to be processed
                    setTimeout(() => {
                        window.location.reload();
                    }, 100);
                }

                // Close the dialog
                setIsNewDoctorDialogOpen(false);
                setIsProcessing(false);
                return;
            }

            // This is a completely new doctor
            console.log("Creating new doctor record");

            // Combine first and last name with proper capitalization
            const firstName = capitalizeName(newDoctor.firstName.trim());
            const lastName = capitalizeName(newDoctor.lastName.trim());
            const fullName = `${firstName} ${lastName}`.trim();

            // Prepare the doctor data
            const doctorData: Omit<Doctor, "id"> = {
                ...newDoctor,
                name: fullName, // Use the combined name
                specialties: newDoctor.specialties as Specialty[],
                initial: firstName.charAt(0).toUpperCase(),
                consultationTypes: [],
                workingDays: defaultWorkingDays,
                onlineBookingAdvanceDays: 60,
                onlineBookingMinHours: 2,
                appointmentSlotDuration: 15,
            };

            // Add the doctor and get the updated doctor with ID
            const newDoctorWithId = addDoctor(doctorData);

            // Register this new doctor in the ID map
            if (newDoctorWithId && newDoctorWithId.mn) {
                storage.registerDoctorId(newDoctorWithId.mn, newDoctorWithId.id);
            }

            // Also make sure the new doctor is assigned to the active medical center
            if (newDoctorWithId) {
                storage.addDoctorToMedicalCenter(newDoctorWithId.id, activeMedicalCenterId);

                // Create a doctor user account - email is now mandatory
                // Check if a user with this email already exists
                const existingUser = storage.getUserByEmail(newDoctorWithId.email!);

                if (!existingUser) {
                    // Generate a temporary password
                    const tempPassword = `temp${Math.floor(100000 + Math.random() * 900000)}`;

                    // Create the doctor user
                    const doctorUser: User = {
                        id: generateUserId(),
                        name: newDoctorWithId.name,
                        email: newDoctorWithId.email!,
                        password: tempPassword,
                        roles: UserRole.DOCTOR,
                        medicalCenterId: activeMedicalCenterId,
                        doctorId: newDoctorWithId.id,
                        phone: newDoctorWithId.phone,
                        dni: newDoctorWithId.dni,
                        permissions: {...defaultDoctorPermissions}
                    };

                    // Save the doctor user
                    storage.saveUser(doctorUser);

                    // Create patient profile if phone and DNI are provided
                    if (newDoctorWithId.phone && newDoctorWithId.dni && patientContext) {
                        const patientId = patientContext.createPatientForProfessional(doctorUser);
                        if (patientId) {
                            // Update the user with the default patient ID
                            const updatedUser = {...doctorUser, defaultPatientId: patientId};
                            storage.saveUser(updatedUser);
                            console.log(`Created patient profile ${patientId} for doctor ${doctorUser.name}`);
                        }
                    }

                    // Send email invitation to the doctor
                    const emailSent = await sendDoctorInvitationEmail(doctorUser.email, tempPassword, doctorUser.name);
                    console.log(`Email invitation ${emailSent ? 'sent' : 'failed'} to ${doctorUser.email}`);

                    // Show success message
                    const hasPatientProfile = newDoctorWithId.phone && newDoctorWithId.dni;
                    toast.success(
                        <div>
                            <p>Doctor creado exitosamente</p>
                            <p className="text-xs mt-1">Credenciales temporales enviadas por email
                                a {doctorUser.email}</p>
                            {hasPatientProfile && (
                                <p className="text-xs mt-1 text-green-600">Perfil de paciente creado automáticamente</p>
                            )}
                        </div>
                    );
                } else {
                    // If user exists but is not a doctor, update their role
                    if (existingUser.roles !== UserRole.DOCTOR) {
                        const updatedUser: User = {
                            ...existingUser,
                            roles: UserRole.DOCTOR,
                            doctorId: newDoctorWithId.id,
                            permissions: {...defaultDoctorPermissions}
                        };

                        // Save the updated user
                        storage.saveUser(updatedUser);

                        toast.success(`Usuario existente actualizado como doctor`);
                    } else {
                        // User already exists as a doctor
                        toast.info(`El usuario ya existe como doctor`);
                    }
                }
            }

            // Validate storage to ensure consistency
            storage.validateStorage();

            // Close dialog
            setIsNewDoctorDialogOpen(false);
        } catch (error) {
            console.error("Error creating/adding doctor:", error);
            toast.error(`Error al crear doctor: ${error}`);
        } finally {
            setIsProcessing(false);
        }
    };

    return (
        <Dialog open={isNewDoctorDialogOpen} onOpenChange={setIsNewDoctorDialogOpen}>
            <DialogContent className="max-w-[48rem] p-6 bg-white shadow-md">
                <DialogHeader>
                    <DialogTitle className="text-xl text-gray-900">Agregar Nuevo Profesional</DialogTitle>
                    <DialogDescription className="text-gray-600">
                        Busque un profesional por M.N. o complete la información básica para crear uno nuevo.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4 py-4">
                    <div className="flex gap-4 items-end">
                        <div className="flex-1 space-y-2">
                            <Label htmlFor="mn">M.N. (Matrícula Nacional)</Label>
                            <Input
                                id="mn"
                                value={newDoctor.mn}
                                onChange={(e) => {
                                    // Only allow numbers
                                    const numericValue = e.target.value.replace(/[^0-9]/g, '');
                                    handleMNChange(numericValue);
                                }}
                                placeholder="Ej: 12345"
                                className="border-gray-300 focus:ring-blue-500"
                                pattern="[0-9]*"
                                inputMode="numeric"
                            />
                        </div>
                        <Button
                            type="button"
                            onClick={handleSearchByMN}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4"
                        >
                            <Search className="h-4 w-4 mr-2"/>
                            Buscar
                        </Button>
                    </div>
                    {searchResult === "found" && (
                        <div className="bg-green-100 text-green-800 p-2 rounded-md text-sm">
                            Profesional encontrado. Seleccione las especialidades que ejercerá en este establecimiento.
                        </div>
                    )}
                    {searchResult === "not-found" && (
                        <div className="bg-yellow-100 text-yellow-800 p-2 rounded-md text-sm">
                            Profesional no encontrado. Completar los datos para crearlo.
                        </div>
                    )}
                    {isDuplicate && (
                        <div className="bg-red-100 text-red-800 p-2 rounded-md text-sm">
                            Este profesional ya existe en el establecimiento.
                        </div>
                    )}

                    <div className="flex gap-4">
                        <div className="w-24 space-y-2">
                            <Label htmlFor="title">Título</Label>
                            <Select
                                value={newDoctor.title}
                                onValueChange={(value: "Dr" | "Dra") => setNewDoctor({...newDoctor, title: value})}
                                disabled={!mnSearched || isDuplicate || searchResult === "found"}
                            >
                                <SelectTrigger className="w-full border-gray-300">
                                    <SelectValue/>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Dr">Dr.</SelectItem>
                                    <SelectItem value="Dra">Dra.</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex-1 space-y-2">
                            <Label htmlFor="firstName">Nombre</Label>
                            <Input
                                id="firstName"
                                value={newDoctor.firstName || ""}
                                onChange={(e) => setNewDoctor({
                                    ...newDoctor,
                                    firstName: capitalizeName(e.target.value)
                                })}
                                placeholder="Ej: Juan"
                                className="border-gray-300 focus:ring-blue-500"
                                disabled={!mnSearched || isDuplicate || searchResult === "found"}
                            />
                        </div>
                        <div className="flex-1 space-y-2">
                            <Label htmlFor="lastName">Apellido</Label>
                            <Input
                                id="lastName"
                                value={newDoctor.lastName || ""}
                                onChange={(e) => setNewDoctor({...newDoctor, lastName: capitalizeName(e.target.value)})}
                                placeholder="Ej: Pérez"
                                className="border-gray-300 focus:ring-blue-500"
                                disabled={!mnSearched || isDuplicate || searchResult === "found"}
                            />
                        </div>
                    </div>

                    <div className="space-y-2" ref={specialtyRef}>
                        <Label>Especialidades</Label>
                        <Button
                            type="button"
                            onClick={() => setShowSpecialtyDropdown(true)}
                            disabled={!mnSearched || isDuplicate}
                            className="bg-blue-600 hover:bg-blue-700 text-white w-full"
                        >
                            <Plus className="h-4 w-4 mr-2"/>
                            Seleccionar Especialidades
                        </Button>
                        {showSpecialtyDropdown && (
                            <div
                                className="absolute z-20 w-full max-w-[45rem] bg-white border border-gray-200 rounded-md mt-1 shadow-lg">
                                <div className="max-h-[15rem] overflow-auto p-2">
                                    {SPECIALTIES.map((specialty) => (
                                        <div
                                            key={specialty}
                                            className={`p-2 text-sm cursor-pointer transition-colors ${
                                                selectedSpecialties.includes(specialty) || newDoctor.specialties.includes(specialty)
                                                    ? "bg-blue-500 text-white hover:bg-blue-600"
                                                    : "hover:bg-gray-100"
                                            }`}
                                            onClick={() => handleSpecialtySelect(specialty)}
                                        >
                                            {specialty}
                                        </div>
                                    ))}
                                </div>
                                <div className="border-t p-2 flex justify-between">
                                    <Button
                                        variant="outline"
                                        onClick={() => setShowSpecialtyDropdown(false)}
                                        className="w-1/2 mr-1"
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        onClick={confirmSpecialties}
                                        className="w-1/2 ml-1 bg-blue-500 hover:bg-blue-600 text-white"
                                    >
                                        Confirmar
                                    </Button>
                                </div>
                            </div>
                        )}
                        <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 min-h-[100px] mt-2">
                            {newDoctor.specialties.length === 0 ? (
                                <div className="text-center py-4 text-gray-500">
                                    <p>No hay especialidades configuradas</p>
                                    <p className="text-sm">Agregue al menos una especialidad</p>
                                </div>
                            ) : (
                                <div className="flex flex-wrap gap-2">
                                    {newDoctor.specialties.map((specialty) => (
                                        <Badge
                                            key={specialty}
                                            className="px-3 py-1 bg-white text-blue-700 hover:bg-blue-100 border-blue-200 flex items-center gap-1"
                                        >
                                            <span>{specialty}</span>
                                            <button
                                                onClick={() => removeSpecialty(specialty)}
                                                className="ml-1 text-blue-600 hover:text-blue-800 rounded-full w-4 h-4 inline-flex items-center justify-center"
                                                disabled={!mnSearched || isDuplicate}
                                            >
                                                <X className="h-3 w-3"/>
                                            </button>
                                        </Badge>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="email">Email *</Label>
                        <Input
                            id="email"
                            type="email"
                            value={newDoctor.email}
                            onChange={(e) => setNewDoctor({...newDoctor, email: e.target.value})}
                            placeholder="Ej: <EMAIL>"
                            className="border-gray-300 focus:ring-blue-500"
                            disabled={!mnSearched || isDuplicate || searchResult === "found"}
                        />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="phone">Teléfono (Opcional)</Label>
                            <div className="custom-phone-input">
                                <PhoneInput
                                    defaultCountry="ar"
                                    value={newDoctor.phone}
                                    onChange={(phone) => {
                                        setNewDoctor({...newDoctor, phone});
                                        setPhoneError("");

                                        try {
                                            // Parse the phone number using Google's libphonenumber
                                            const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                                            const isValid = phoneUtil.isValidNumber(phoneNumber);

                                            if (!isValid && phone !== "+" && phone.length > 5) {
                                                setPhoneError("El número de teléfono no es válido");
                                            }
                                        } catch {
                                            if (phone !== "+" && phone.length > 5) {
                                                setPhoneError("El número de teléfono no es válido");
                                            }
                                        }
                                    }}
                                    inputStyle={{
                                        width: '100%',
                                        height: '2.5rem'
                                    }}
                                    className="w-full custom-phone-input with-dial-code-preview"
                                    placeholder="Teléfono"
                                    countrySelectorStyleProps={{
                                        buttonStyle: {
                                            paddingLeft: '10px',
                                            paddingRight: '5px'
                                        }
                                    }}
                                    hideDropdown={false}
                                    disableDialCodeAndPrefix={true}
                                    showDisabledDialCodeAndPrefix={true}
                                    disableFormatting={false}
                                    preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                                    countries={getSpanishCountries()}
                                    disabled={!mnSearched || isDuplicate || searchResult === "found"}
                                />
                                {phoneError && (
                                    <div className="text-xs text-red-600 mt-1">
                                        {phoneError}
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="dni">DNI (Opcional)</Label>
                            <Input
                                id="dni"
                                type="text"
                                value={newDoctor.dni}
                                onChange={(e) => setNewDoctor({...newDoctor, dni: e.target.value})}
                                placeholder="Ej: 12345678"
                                className="border-gray-300 focus:ring-blue-500"
                                disabled={!mnSearched || isDuplicate || searchResult === "found"}
                            />
                        </div>
                    </div>
                </div>

                <DialogFooter className="">
                    <Button
                        variant="outline"
                        onClick={() => setIsNewDoctorDialogOpen(false)}
                        className="border-gray-300 text-gray-700 hover:bg-gray-100"
                        disabled={isProcessing}
                    >
                        Cancelar
                    </Button>
                    <Button
                        onClick={handleCreateNewDoctor}
                        disabled={
                            !mnSearched ||
                            !newDoctor.firstName ||
                            !newDoctor.lastName ||
                            !newDoctor.mn ||
                            !newDoctor.email ||
                            newDoctor.specialties.length === 0 ||
                            isDuplicate ||
                            isProcessing
                        }
                        className={`bg-blue-600 hover:bg-blue-700 text-white ${isProcessing ? 'opacity-75' : ''}`}
                    >
                        {isProcessing ? (
                            <>Procesando...</>
                        ) : (
                            <>
                                <Plus className="h-4 w-4 mr-2"/>
                                {searchResult === "found" ? "Agregar Profesional" : "Crear Profesional"}
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}