"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { useState, useEffect, useRef } from "react"
import { Menu, X } from "lucide-react"
import { usePathname } from 'next/navigation'
import { useAuth } from "@/contexts/AuthContext"
import { PatientUserPill } from "@/components/ui/PatientUserPill"
import { usePatients } from "@/contexts/PatientContext"
import { Auth0LoginButton } from "@/components/auth/Auth0LoginButton"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const headerRef = useRef<HTMLDivElement>(null)
  const pathname = usePathname()
  const isLandingPage = pathname === '/'
  const { currentUser, logout, loginWithAuth0 } = useAuth()
  const { getPatientById } = usePatients()

  // Add scroll event listener with throttling to improve performance
  useEffect(() => {
    let lastScrollY = window.scrollY
    let ticking = false

    const handleScroll = () => {
      lastScrollY = window.scrollY

      if (!ticking) {
        window.requestAnimationFrame(() => {
          const isScrolled = lastScrollY > 20
          if (isScrolled !== scrolled) {
            setScrolled(isScrolled)
          }
          ticking = false
        })

        ticking = true
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [scrolled])

  // Auth buttons/user pill component to reuse in both header states
  const AuthButtons = () => (
    <div className="flex items-center gap-2">
      {currentUser ? (
        <>
          <Button variant="ghost" asChild className="hidden md:inline-flex text-sm hover:bg-blue-50 hover:text-blue-600">
            <Link href="/plataforma/paciente">Mis turnos</Link>
          </Button>
          <PatientUserPill
            currentUser={currentUser}
            currentPatient={null} //TODO FACU sacar patient id
            logout={logout}
            variant={scrolled ? "light" : "dark"}
          />
        </>
      ) : (
        <>
          {/* Desktop buttons */}
          <Button
            variant="ghost"
            onClick={loginWithAuth0}
            className="hidden md:inline-flex text-sm hover:bg-transparent hover:text-blue-600"
          >
            Iniciar sesión
          </Button>
          <Button
            variant="default"
            className="hidden md:inline-flex rounded-full bg-blue-600 hover:bg-blue-700 text-white border border-blue-600 px-4 py-2 text-sm shadow-md hover:shadow-lg transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60 focus-visible:ring-offset-1"
            size="sm"
            asChild
          >
            <Link href="/plataforma/registro">Registrarme</Link>
          </Button>
          {/* Mobile register button reinstated */}
          <Button
            variant="default"
            className="md:hidden inline-flex rounded-full bg-blue-600 hover:bg-blue-700 text-white border border-blue-600 px-4 py-2 text-sm shadow-md transition-all"
            size="sm"
            asChild
          >
            <Link href="/plataforma/registro">Registrarme</Link>
          </Button>
        </>
      )}
    </div>
  )

  return (
    <>
      {/* Fixed height placeholder to prevent content jumping */}
      <div className="h-[66px] md:h-[90px] w-full" />

      {/* Frosty backdrop - always visible on mobile, appears when scrolling on desktop */}
      <div
        className={`
          fixed top-0 left-0 right-0 w-full z-[60]
          backdrop-blur-sm bg-blue-50/70
          transition-opacity duration-700 ease-in-out
          h-[60px]
          md:${scrolled ? 'opacity-100' : 'opacity-0'}
          opacity-100
        `}
      />

      {/* Header container - always fixed */}
      <header
        ref={headerRef}
        className={`
          fixed top-0 left-0 right-0 z-[70]
          transition-all duration-700 ease-in-out
        `}
      >
        <div
          className={`
            max-w-6xl mx-auto px-7 flex justify-between items-center
            transition-all duration-700 ease-in-out h-[60px]
            ${scrolled ? 'mt-0' : 'mt-0 md:mt-10'}
            ${!scrolled ? 'md:bg-gradient-to-b md:from-white md:via-slate-50 md:to-blue-50 md:rounded-full md:shadow-2xl md:shadow-slate-400/30 md:border md:border-slate-300/80 md:h-[66px]' : ''}
          `}
        >
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera"
                width={100}
                height={30}
                priority
                className="h-8 md:h-7 w-auto"
              />
            </Link>
          </div>

          {/* Desktop navigation - only visible on desktop when not scrolled and on landing page */}
          {isLandingPage && (
            <nav
              className={`
                hidden md:flex space-x-5 items-center absolute
                transition-all duration-700 ease-in-out
                ${scrolled
                  ? 'opacity-0 transform -translate-y-4 pointer-events-none'
                  : 'opacity-100 transform translate-y-0'
                }
                left-1/2 -translate-x-1/2
              `}
            >
              <Link href="#buscar" className="text-slate-800 hover:text-blue-600 text-sm">
                Reservar un turno
              </Link>
              <Link href="#coberturas" className="text-slate-800 hover:text-blue-600 text-sm">
                Coberturas
              </Link>
              <Link
                href="/para-profesionales"
                className="text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-500 hover:to-teal-400 px-3 py-1 rounded-full border border-blue-100"
              >
                Soy profesional
              </Link>
            </nav>
          )}

          {/* Auth buttons and mobile menu */}
          <div className="flex items-center gap-2">
            <AuthButtons />
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 md:hidden ml-2"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>

          {/* Mobile menu dropdown */}
          {isMenuOpen && (
            <div
              className="
                absolute top-full left-0 right-0 mx-4 bg-white shadow-lg rounded-xl md:hidden z-[80]
                mt-2
              "
            >
              <div className="flex flex-col p-4 space-y-4">
                {isLandingPage && (
                  <>
                    <Link href="#buscar" className="mx-3 text-slate-800 hover:text-blue-600 text-lg py-2">
                      Buscar
                    </Link>
                    <Link href="#coberturas" className="mx-3 text-slate-800 hover:text-blue-600 text-lg py-2">
                      Coberturas
                    </Link>
                    <Link
                      href="/para-profesionales"
                      className="mx-3 font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-700 hover:to-teal-600 text-lg py-2"
                    >
                      Soy profesional
                    </Link>
                  </>
                )}
                {!currentUser && (
                  <>
                    <div className="mx-2 border-t border-slate-200/80" />
                    <div className="px-3 text-xs uppercase tracking-wide text-slate-500/90">Tu cuenta</div>
                    <div className="flex flex-col gap-2 px-2">
                      <Auth0LoginButton
                        className="w-full rounded-full bg-white/70 backdrop-blur border border-blue-600 text-blue-700 hover:bg-white/90 hover:border-blue-600 hover:text-blue-700 px-4 py-3 text-base shadow-sm transition-all duration-200 group focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50 focus-visible:ring-offset-1"
                      >
                        <span className="font-medium">Iniciar sesión</span>
                      </Auth0LoginButton>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </header>
    </>
  )
}

