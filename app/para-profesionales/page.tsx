"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
// Removed Card in favor of custom surfaces aligned with landing styles
import { Clock, Brain, DollarSign, Check, ArrowRight, CalendarClock, Users, Star, CalendarCheck, UserCircle, BellRing, Menu, X, Calendar, MessageSquare } from "lucide-react"
import Link from "next/link"
import { useEffect, useState, useRef } from 'react'
import { ProfessionalRegister } from "@/components/paraprofesionales/professional-register"

export default function DoctorsLandingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [showRegister, setShowRegister] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const headerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    document.title = "Turnera - Agenda de turnos para profesionales de la salud."
  }, [])

  // Add scroll event listener with throttling to improve performance
  useEffect(() => {
    let lastScrollY = window.scrollY
    let ticking = false

    const handleScroll = () => {
      lastScrollY = window.scrollY

      if (!ticking) {
        window.requestAnimationFrame(() => {
          const isScrolled = lastScrollY > 20
          if (isScrolled !== scrolled) {
            setScrolled(isScrolled)
          }
          ticking = false
        })

        ticking = true
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [scrolled])

  // Show the registration dialog
  const handleRegisterClick = (e: React.MouseEvent) => {
    e.preventDefault()
    setShowRegister(true)
  }

  interface FeatureProps {
    icon: string | React.ReactNode;
    title: string;
    description: string;
  }

  const pricingConfig = {
    packs: [
      { quantity: 20, price: 5500 },
      { quantity: 50, price: 12500 },
      { quantity: 100, price: 24000 },
      { quantity: 200, price: 40000 },
      { quantity: 500, price: 90000 },
      { quantity: 1000, price: 150000 },
      { quantity: 1500, price: 160000 },
    ],
    // newPatientFee: 500,  
    individualReminderPrice: 300,
  };

  const basePrice = pricingConfig.packs[0].price / pricingConfig.packs[0].quantity;

  const calculatedPricing = pricingConfig.packs.map(pack => {
    const unitPrice = Math.round(pack.price / pack.quantity);
    const discountPercent = Math.round((1 - unitPrice / basePrice) * 100);
    return {
      ...pack,
      unitPrice,
      discountPercent,
      displayPrice: pack.price.toLocaleString(),
    };
  });
  const lowestPackUnitPrice = Math.min(...calculatedPricing.map((pack) => pack.unitPrice));

  const Feature = ({ icon, title, description }: FeatureProps) => {
    return (
      <div className="flex items-start gap-4 md:gap-5">
        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50 ring-1 ring-blue-200">
          {typeof icon === 'string' ? (
            <Image src={icon} alt={title} width={18} height={18} />
          ) : (
            icon
          )}
        </div>
        <div>
          <h4 className="font-semibold font-recoleta text-base md:text-lg text-gray-900 m-0">{title}</h4>
          <p className="text-gray-600 text-sm md:text-base leading-relaxed">{description}</p>
        </div>
      </div>
    );
  };

  return (
    <>
      <style jsx global>{`
        html, body {
          overflow-x: hidden;
        }
        html {
          font-size: 13px;
        }
        @media (min-width: 1500px) {
          html {
            font-size: 15px;
          }
        }
        @media (min-width: 2560px) {
          html {
            font-size: 17px;
          }
        }
        @keyframes float {
          0% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-8px);
          }
          100% {
            transform: translateY(0px);
          }
          animation-timing-function: ease-in-out;
        }
  .background-container {
    background-color: #3B82F6;
    border-radius: 20px;
    width: 85%;
    height: 400px;
    position: relative;
    overflow: hidden;
  }
  .image-style {
    width: 100%;
    height: auto;
    position: absolute;
    bottom: 0;
    right: 0;
  }
  .phone-background-container {
    background-color: #3B82F6;
    border-radius: 20px;
    width: 500px;
    height: 500px;
    position: relative;
    overflow: visible;
-webkit-mask-image: linear-gradient(to bottom, black 0%, rgba(0, 0, 0, 0.85) 80%, transparent 100%); /* More gradual fade */
    mask-image: linear-gradient(to bottom, black 0%, rgba(0, 0, 0, 0.85) 80%, transparent 100%);
  }
  .phone-image-style {
    width: 120%;
    height: auto;
    position: absolute;
    bottom: -120px;
    left: 50%;
    transform: translateX(-50%);
  }
      `}</style>
    <main className="flex min-h-screen flex-col bg-white">
      {/* Header and Hero with gradient background */}
      <div className="bg-gradient-to-br from-white to-blue-50">
        {/* Fixed height placeholder to prevent content jumping */}
        <div className="h-[66px] md:h-[90px] w-full" />

        {/* Frosty backdrop - always visible on mobile, appears when scrolling on desktop */}
        <div
          className={`
            fixed top-0 left-0 right-0 w-full z-40
            backdrop-blur-sm bg-white/70
            transition-opacity duration-700 ease-in-out
            h-[60px]
            ${scrolled ? 'md:opacity-100' : 'md:opacity-0'}
            md:pointer-events-${scrolled ? 'auto' : 'none'}
            block md:block
          `}
        />

        {/* Header - Styled like the landing page's header */}
        <header
          ref={headerRef}
          className={`
            fixed top-0 left-0 right-0 z-50
            transition-all duration-700 ease-in-out
          `}
        >
          <div
            className={`
              max-w-6xl mx-auto px-7 flex justify-between items-center
              transition-all duration-700 ease-in-out h-[60px]
              ${scrolled ? 'mt-0' : 'mt-0 md:mt-10'}
              ${!scrolled ? 'md:bg-gradient-to-b md:from-white md:via-slate-50 md:to-blue-50 md:rounded-full md:shadow-2xl md:shadow-slate-400/30 md:border md:border-slate-300/80 md:h-[66px]' : ''}
            `}
          >
            {/* Mobile layout - logo and menu */}
            <div className="flex items-center">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="p-2 md:hidden"
              >
                {isMenuOpen ? (
                  <X className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
              </button>
              <Link href="/">
                <Image
                  src="/images/turnera-logo.svg"
                  alt="Turnera"
                  width={100}
                  height={30}
                  priority
                  className="h-7 w-auto"
                />
              </Link>
            </div>

            {/* Desktop navigation - only visible on desktop when not scrolled */}
            <nav
              className={`
                hidden md:flex space-x-5 items-center absolute
                transition-all duration-700 ease-in-out
                ${scrolled
                  ? 'opacity-0 transform -translate-y-4 pointer-events-none'
                  : 'opacity-100 transform translate-y-0'
                }
                left-1/2 -translate-x-1/2
              `}
            >
              <a href="#beneficios" className="text-[#1a2b60] hover:text-blue-700 text-sm">
                Beneficios
              </a>
              <a href="#caracteristicas" className="text-[#1a2b60] hover:text-blue-700 text-sm">
                Características
              </a>
              <a href="#precios" className="text-[#1a2b60] hover:text-blue-700 text-sm">
                Precios
              </a>
            </nav>

            {/* No navigation links in the frosted version, just like in Hero.tsx */}

            {/* Auth buttons */}
            <div className="flex items-center gap-3">
              <Button
                className={`
                  rounded-[12px] bg-blue-600 text-white border border-blue-700 hover:bg-blue-700 shadow-sm px-4
                  transition-all duration-700 ease-in-out
                `}
                size="sm"
                onClick={handleRegisterClick}
              >
                Unirme Gratis
              </Button>
              <Button
                variant="outline"
                className={`
                  rounded-[12px] text-blue-600 border-blue-300 hover:border-blue-600 hover:bg-blue-50
                  transition-all duration-700 ease-in-out
                `}
                size="sm"
                asChild
              >
                <Link href="/plataforma/establecimiento/login">Iniciar sesión</Link>
              </Button>
            </div>

            {/* Mobile menu dropdown */}
            {isMenuOpen && (
              <div className="absolute top-full left-0 right-0 mx-4 bg-white shadow-lg rounded-xl mt-2 md:hidden z-50">
                <div className="flex flex-col p-4 space-y-4">
                  <a href="#beneficios" className="mx-3 text-gray-600 hover:text-blue-600">
                    Beneficios
                  </a>
                  <a href="#caracteristicas" className="mx-3 text-gray-600 hover:text-blue-600">
                    Características
                  </a>
                  <a href="#precios" className="mx-3 text-gray-600 hover:text-blue-600">
                    Precios
                  </a>
                </div>
              </div>
            )}
          </div>
        </header>

        {/* Hero Section */}
        <section className="pb-10 pt-16 md:pt-24 md:pb-20">
          <div className="container mx-auto px-8 md:px-16 md:pl-24 max-w-[90rem]">
            <div className="flex flex-col-reverse md:flex-row items-center">
              <div className="md:w-1/2 md:pr-8 text-center md:text-left">
                <h1 className="text-3xl md:text-5xl mb-3 md:mb-6 font-recoleta">
                  Tu consultorio más visible, <span className="font-semibold text-blue-600">tus pacientes más cerca</span>.
                </h1>
                <p className="md:text-base text-base mb-8 max-w-2xl text-gray-600 mx-auto md:mx-0">
                Sumá pacientes y automatizá tu agenda 24/7. Sin llamadas telefónicas, sin complicaciones.
                </p>
                <div className="flex flex-col sm:flex-row justify-center md:justify-start gap-4 mb-8 px-24 md:px-0 sm:px-0">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-base" onClick={handleRegisterClick}>
                    Comenzar gratis
                  </Button>
                  <Button size="lg" variant="outline" className="border-blue-200 text-blue-600 hover:bg-blue-50 text-base" asChild>
                    <a href="#preguntas-frecuentes">Preguntas frecuentes</a>
                  </Button>
                </div>
                <div className="flex flex-wrap justify-center md:justify-start items-center text-sm text-gray-600">
                  <span>Sin costos fijos</span>
                  <span className="mx-2 text-blue-600">•</span>
                  <span>Agenda online y offline</span>
                  <span className="mx-2 text-blue-600">•</span>
                  <span>Cancelá cuando quieras</span>
                </div>
{/*                 <div className="mt-8">
              <p className="mb-3 text-xs uppercase tracking-wide text-slate-500">Confían en turnera</p>
              <div className="flex flex-wrap items-center gap-4 opacity-80">
                <Image src="/images/logos/osde.png" alt="OSDE" width={72} height={24} />
                <Image src="/images/logos/medicus.png" alt="Medicus" width={80} height={24} />
                <Image src="/images/logos/galeno.png" alt="Galeno" width={70} height={22} />
                <Image src="/images/logos/omint.png" alt="Omint" width={70} height={22} />
                <Image src="/images/logos/medife.png" alt="Medife" width={70} height={22} />
              </div>
              </div> */}
              </div>
              <div className="md:w-1/2 mb-8 md:mb-0 md:mt-0 relative">
                <div className="relative z-10">
                  <Image
                    src="/images/doctores-profesional.svg"
                    alt="Doctor illustration"
                    width={400}
                    height={400}
                    className="w-[85%] md:w-[85%] h-auto mx-auto md:ml-auto md:mr-0"
                  />
                </div>
                {/* Floating elements */}
                <div className="absolute top-8 left-2 md:top-10 md:left-12 bg-white p-2.5 md:p-4 rounded-xl md:rounded-2xl shadow-xl animate-float z-20" style={{animationDelay: '0.5s'}}>
                  <div className="flex items-center gap-2.5 md:gap-3">
                    <div className="w-2.5 h-2.5 md:w-3 md:h-3 bg-green-500 rounded-full"></div>
                    <span className="text-xs md:text-sm font-medium">Nuevo paciente</span>
                  </div>
                </div>
                <div className="absolute top-28 right-2 md:top-32 md:-right-8 bg-white p-2.5 md:p-4 rounded-xl md:rounded-2xl shadow-xl animate-float z-20" style={{animationDelay: '1s'}}>
                  <div className="flex items-center gap-2.5 md:gap-3">
                    <Calendar className="w-4 h-4 md:w-5 md:h-5 text-blue-600" />
                    <span className="text-xs md:text-sm font-medium">Turno confirmado</span>
                  </div>
                </div>
                <div className="absolute bottom-2 left-2 md:bottom-20 md:-left-2 bg-white p-2.5 md:p-4 rounded-xl md:rounded-2xl shadow-xl animate-float z-20" style={{animationDelay: '1.5s'}}>
                  <div className="flex items-center gap-2.5 md:gap-3">
                    <MessageSquare className="w-4 h-4 md:w-5 md:h-5 text-green-600" />
                    <span className="text-xs md:text-sm font-medium">Recordatorio enviado</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Benefits Section with Wavy SVG */}
      <section id="beneficios" className="py-16 relative overflow-hidden mb-8">
        <div className="absolute inset-0 bg-gradient-to-bl from-blue-50 to-white z-0"></div>
        <svg className="absolute bottom-0 left-0 w-full opacity-10 z-0" viewBox="0 0 1440 320" preserveAspectRatio="none">
          <path fill="#3B82F6" fillOpacity="0.5" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,170.7C960,160,1056,192,1152,197.3C1248,203,1344,181,1392,170.7L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          <path fill="#3B82F6" fillOpacity="0.3" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,186.7C960,192,1056,224,1152,229.3C1248,235,1344,213,1392,202.7L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
        <div className="container mx-auto px-8 md:px-6 max-w-7xl relative z-10 text-center">
          <h2 className="text-3xl md:text-4xl mb-4 font-recoleta text-gray-900 text-center px-8 md:px-0">Tu agenda médica, <span className="font-bold z-10">simplificada</span></h2>
          <p className="text-base md:text-lg text-gray-600 max-w-2xl mx-auto mb-12">
            Turnera te ayuda a sumar pacientes, ahorrar costos y reducir ausencias, seas independiente o tengas un establecimiento médico.
          </p>
          <div className="absolute -right-12 -top-12 w-24 h-24 bg-blue-100 rounded-full opacity-60 group-hover:opacity-100 transition-opacity z-0"></div>
          <div className="px-0 mx-auto max-w-7xl grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8">
            {[
              {
                icon: <CalendarCheck className="w-6 h-6 text-blue-600" />,
                title: "Disponible todos los días",
                description: "Recibí turnos de pacientes a toda hora, los 365 días del año."
              },
              {
                icon: <Brain className="w-6 h-6 text-blue-600" />,
                title: "Agenda inteligente",
                description: "Optimizamos la disponibilidad para evitar espacios vacíos entre paciente y paciente."
              },
              {
                icon: <DollarSign className="w-6 h-6 text-blue-600" />,
                title: "Pagá sólo lo que usás",
                description: "Sólo pagás por nuevos pacientes y por recordatorios enviados. Sin sorpresas.¹"
              },
            ].map((benefit, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 md:p-8 text-left shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"
              >
                <div className="absolute -right-10 -top-10 h-24 w-24 rounded-full bg-blue-50 opacity-40 transition-opacity group-hover:opacity-70" />
                <div className="absolute -left-12 -bottom-10 h-24 w-24 rounded-full bg-indigo-50 opacity-30" />

                <div className="relative mb-3 md:mb-4 flex items-center gap-4 md:gap-5">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 ring-1 ring-blue-200">
                    {benefit.icon}
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold font-recoleta text-gray-900 m-0">{benefit.title}</h3>
                </div>
                <p className="text-gray-600 text-sm md:text-base leading-relaxed">{benefit.description}</p>
                <div className="mt-5 h-1 w-12 rounded-full bg-gradient-to-r from-blue-600 to-blue-300 transition-all group-hover:w-16" />
              </div>
            ))}
          </div>
        </div>
      </section>

<section id="caracteristicas" className="py-6 relative mb-8 md:mb-12">
  <div className="absolute inset-0 bg-white z-0"></div>
  <div className="absolute top-0 right-0 w-96 h-96 bg-blue-100 rounded-full opacity-30 blur-3xl -translate-y-1/4 translate-x-1/4 z-0"></div>
  <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-100 rounded-full opacity-30 blur-3xl translate-y-1/4 -translate-x-1/4 z-0"></div>
  <div className="container mx-auto px-4 md:px-6 relative z-10">
    <div className="text-center">
      <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wider mb-3 md:mb-4 inline-block">Características</span>
    </div>
    <div className="mb-6 md:mb-1 md:px-0 px-5">
      <h2 className="text-3xl md:text-4xl mb-4 font-recoleta text-center text-gray-900">Tu agenda y nuevos pacientes, <span className="font-bold z-10">con turnera</span></h2>
    </div>

    {/* First Feature Item */}
    <div className="md:px-4 px-4 max-w-[90rem] mx-auto mb-6 py-8 md:py-6 md:mb-16"> {/* Reduced mb-8 to mb-6 for mobile */}
      <div className="grid md:grid-cols-2 gap-4 md:gap-8 items-center">
        {/* Image - Reduced bottom margin on mobile */}
        <div className="md:order-2 w-[90%] h-[300px] md:h-[450px] relative overflow-visible -mb-10 md:mb-4 mx-auto md:mx-0"> {/* Changed mb-4 to mb-2 for mobile */}
          <Image
            src="/images/turnera-agenda-2-bg-azul.png"
            alt="Gestión de agenda médica"
            width={1200}
            height={650}
            className="rounded-2xl w-full h-auto absolute hover:shadow-xl transition-all duration-300 group hover:-translate-y-1"
          />
        </div>
        {/* Content */}
        <div className="md:order-1 md:pl-20">
          <h3 className="text-2xl md:text-3xl font-recoleta mb-2 md:mb-4 font-semibold text-center md:text-left">Todo tu día organizado</h3>
          <p className="text-base md:text-base text-gray-600 mb-3 md:mb-6 max-w-xl mx-auto md:mx-0">
          Administrá tu agenda médica desde un sólo lugar, ya seas un profesional independiente o dirijas un establecimiento médico. Turnos, profesionales, consultorios y horarios organizados de manera simple e intuitiva.
          </p>
          <div className="flex justify-center md:justify-start">
            <Button className="flex items-center mb-4 md:mb-8 bg-transparent text-notion-dark hover:bg-gray-100 text-sm">
              Explorá la gestión de agendas <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </div>
          <div className="space-y-3 md:space-y-6">
            <Feature icon={<Users className="w-5 h-5 text-blue-600" />} title="Uno o múltiples profesionales" description="Gestioná los horarios de todo tu equipo médico en una sola plataforma." />
            <Feature icon={<CalendarClock className="w-5 h-5 text-blue-600" />} title="Organización inteligente" description="Optimizá los horarios para maximizar la disponibilidad de tus turnos." />
            <Feature icon={<BellRing className="w-5 h-5 text-blue-600" />} title="Recordatorios automáticos" description="Reducí el ausentismo con recordatorios por WhatsApp, SMS y Email." />
          </div>
          <div className="mt-5 md:mt-8 border-t border-gray-200 pt-3 md:pt-6 max-w-[580px]">
            <p className="font-medium mb-2 text-sm">Reemplaza</p>
            <div className="flex flex-wrap items-center gap-3 md:gap-6">
              <span className="text-sm text-gray-600">Agendas en papel</span>
              <span className="text-sm text-gray-600">Excel</span>
              <span className="text-sm text-gray-600">Llamados telefónicos</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Second Feature Item */}
    <div className="md:px-4 px-4 max-w-8xl mx-auto -mb-8 pt-4 md:py-0 md:pt-0 md:mb-4"> {/* Adjusted mb-2 to mb-4 for slight mobile spacing */}
      <div className="grid md:grid-cols-2 gap-4 md:gap-6 items-center">
        {/* Image */}
        <div className="md:order-1 flex justify-center mb-10 md:mb-0">
          <div className="bg-[#3B82F6] rounded-2xl w-3/5 md:w-full max-w-md aspect-square relative overflow-visible -translate-y-2 md:-translate-y-4 hover:shadow-xl transition-all duration-300 group hover:-translate-y-6">
            <div className="absolute inset-0 scale-110 md:scale-125 translate-y-10 md:translate-y-16">
              <Image
                src="/images/phone-book.png"
                alt="Aplicación móvil"
                fill
                className="object-contain transition-all duration-300 group hover:-translate-y-2 hover:-translate-x-1"
              />
            </div>
          </div>
        </div>
        {/* Content - Added bottom margin on mobile */}
        <div className="md:order-2 mb-6 md:mb-0"> {/* Added mb-6 for mobile */}
          <h3 className="text-2xl md:text-3xl font-recoleta mb-3 md:mb-4 font-semibold text-center md:text-left">Captá nuevos pacientes sin esfuerzo</h3>
          <p className="text-base md:text-base text-gray-600 mb-4 md:mb-6 max-w-xl mx-auto md:mx-0">
            Nuestra plataforma de búsqueda y reserva de turnos conecta a profesionales con pacientes, ampliando tu visibilidad y llenando espacios disponibles en tu agenda de forma automática.
          </p>
          <div className="flex justify-center md:justify-start">
            <Button className="flex items-center mb-4 md:mb-8 bg-transparent text-notion-dark hover:bg-gray-100">
              Conocé cómo aumentar tus pacientes <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </div>
          <div className="space-y-3 md:space-y-6">
            <Feature icon={<UserCircle className="w-5 h-5 text-blue-600" />} title="Perfil profesional" description="Mostrá tus especialidades, prácticas y coberturas que aceptás." />
            <Feature icon={<CalendarCheck className="w-5 h-5 text-blue-600" />} title="Reservas online" description="Tus pacientes pueden reservar turnos las 24 horas, sin intermediarios." />
            <Feature icon={<Star className="w-5 h-5 text-blue-600" />} title="Reseñas y valoraciones" description="Construí tu reputación online y atraé más pacientes." />
          </div>
          <div className="mt-4 md:mt-8 border-t border-gray-200 pt-3 md:pt-6 max-w-[580px] mx-auto md:mx-0">
            <p className="font-medium mb-2">Reemplaza</p>
            <div className="flex flex-wrap items-center gap-3 md:gap-6">
              <span className="text-sm text-gray-600">Publicidad costosa</span>
              <span className="text-sm text-gray-600">Redes sociales</span>
              <span className="text-sm text-gray-600">Directorios obsoletos</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

      {/* Pricing Section with Noise Texture */}
      <section
        id="precios"
        className="py-8 md:py-16 relative bg-white"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3CfeColorMatrix type='matrix' values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px',
        }}
      >
        <div className="container mx-auto px-10 md:px-6 text-center">
          <div className="max-w-3xl mx-auto mb-12">
            <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wider mb-4 inline-block">Precios</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 font-recoleta text-gray-900">
              Pagá sólo por lo que usás
            </h2>
            <p className="text-base md:text-lg text-gray-600 max-w-2xl mx-auto">
             Sin abonos mensuales. Con Turnera sólo pagás cuando realmente obtenés valor.
            </p>
          </div>
          <div className="md:p-0 flex flex-col md:flex-row justify-center items-stretch gap-8 mb-10 mx-auto max-w-5xl">
            <div className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-white p-8 md:p-10 text-left shadow-sm transition-all hover:-translate-y-1 hover:shadow-md w-full md:max-w-md h-[450px] flex flex-col">
              <div className="absolute -right-12 -top-12 w-24 h-24 bg-blue-50 rounded-full opacity-60 group-hover:opacity-80 transition-opacity" />
              <div className="relative mb-4 flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 ring-1 ring-blue-200">
                  <CalendarCheck className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <span className="text-xs font-semibold text-blue-600 block">Para todos</span>
                  <h3 className="text-2xl font-bold font-recoleta text-gray-900">Tu agenda médica, gratis</h3>
                </div>
              </div>
              <div className="mb-6">
                <div className="flex items-center">
                  <span className="text-4xl font-bold font-recoleta text-gray-900">$0</span>
                  <span className="text-gray-600 ml-2">por mes</span>
                </div>
                <p className="text-sm text-gray-500 mt-1">Sin costos ocultos</p>
              </div>
              <ul className="mb-6 text-left space-y-4">
                <li className="flex items-center">
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <Check className="h-4 w-4 text-blue-600" />
                  </div>
                  <span>Agenda médica completa <strong>sin cargo</strong></span>
                </li>
                <li className="flex items-center">
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <Check className="h-4 w-4 text-blue-600" />
                  </div>
                  <span>Turnos manuales ilimitados</span>
                </li>
                <li className="flex items-center">
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <Check className="h-4 w-4 text-blue-600" />
                  </div>
                  <span>Profesionales ilimitados</span>
                </li>
                <li className="flex items-center">
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <Check className="h-4 w-4 text-blue-600" />
                  </div>
                  <span>Panel de gestión intuitivo</span>
                </li>
              </ul>
              <div className="mt-auto">
                <div className="mb-4 h-1 w-12 rounded-full bg-gradient-to-r from-blue-600 to-blue-300" />
                <Button className="w-full rounded-xl py-6 bg-blue-600 hover:bg-blue-700 transition-all duration-300" onClick={handleRegisterClick}>
                  Comenzar gratis
                </Button>
              </div>
            </div>
            <div className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-white p-8 md:p-10 text-left shadow-sm transition-all hover:-translate-y-1 hover:shadow-md w-full md:max-w-md h-[450px] flex flex-col">
              <div className="absolute -right-12 -top-12 w-24 h-24 bg-blue-50 rounded-full opacity-60 group-hover:opacity-80 transition-opacity" />
              <div className="relative mb-4 flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 ring-1 ring-blue-200">
                  <BellRing className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <span className="text-xs font-semibold text-blue-600 block">Minimizá el ausentismo</span>
                  <h3 className="text-2xl font-bold font-recoleta text-gray-900">Recordatorios de turnos</h3>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-3 mb-8 w-full">
                {calculatedPricing.slice(0, 3).map((pack, index) => (
                  <div
                    key={index}
                    className="relative text-center p-3 rounded-xl border ring-1 transition-colors border-gray-200 ring-blue-200 bg-blue-50/30 h-24 md:h-24 flex flex-col items-center justify-between"
                  >
                    <div className="text-[10px] md:text-[11px] uppercase tracking-wide text-gray-500 leading-none">Pack de {pack.quantity}</div>
                    <div className="text-base md:text-lg font-semibold font-recoleta text-gray-900 leading-none">${pack.displayPrice}</div>
                    <div className="text-[10px] md:text-[11px] text-gray-500 leading-none">(${pack.unitPrice} c/u)</div>
                  </div>
                ))}
              </div>
              <ul className="mb-6 text-left space-y-4">
                <li className="flex items-center">
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <Check className="h-4 w-4 text-blue-600" />
                  </div>
                  <span>Recordatorios por WhatsApp, SMS y Email</span>
                </li>
                <li className="flex items-center">
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <Check className="h-4 w-4 text-blue-600" />
                  </div>
                  <span>Envío automático un día antes</span>
                </li>
                <li className="flex items-center">
                  <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <Check className="h-4 w-4 text-blue-600" />
                  </div>
                  <span>Renovación automática opcional</span>
                </li>
              </ul>
              <div className="mt-auto">
                <div className="mb-4 h-1 w-12 rounded-full bg-gradient-to-r from-blue-600 to-blue-300" />
                <Button className="w-full rounded-xl py-6 bg-blue-600 hover:bg-blue-700 transition-all duration-300">
                  Ver todos los packs
                </Button>
              </div>
            </div>
          </div>
          <div className="max-w-4xl mx-auto mb-16">
            <h3 className="text-xl md:text-2xl font-bold mb-6 font-recoleta">Todos los packs de recordatorios</h3>
            <div className="overflow-hidden rounded-xl border border-gray-200 shadow-sm">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-blue-600 text-white">
                      <th className="px-6 py-4 text-left font-medium">Pack</th>
                      <th className="px-6 py-4 text-right font-medium">Precio</th>
                      <th className="px-6 py-4 text-right font-medium">Costo unitario</th>
                      <th className="px-6 py-4 text-right font-medium">Ahorro</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {calculatedPricing.map((pack, index) => (
                      <tr key={index} className="hover:bg-blue-50 transition-colors">
                        <td className="px-6 py-4 font-medium">{pack.quantity} recordatorios</td>
                        <td className="px-6 py-4 text-right">${pack.displayPrice}</td>
                        <td className="px-6 py-4 text-right">${pack.unitPrice}</td>
                        <td className="px-6 py-4 text-right">
                          {pack.discountPercent > 0 ? (
                            <span className="text-green-600 font-medium">{pack.discountPercent}%</span>
                          ) : (
                            '-'
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div className="max-w-4xl mx-auto mb-16">
            <h3 className="text-2xl font-semibold mb-2 font-recoleta">¿Te buscan? Te encuentran.</h3>
            <p className="text-base text-center mb-8 max-w-2xl mx-auto text-gray-600">
              Generá más pacientes a través de nuestra página de búsqueda y reserva de turnos.
            </p>
            <div className="relative mb-8 overflow-hidden rounded-2xl">
              <div className="absolute inset-0 bg-blue-600 opacity-95"></div>
              <div className="absolute inset-0 bg-[url('/images/pattern.svg')] opacity-10"></div>
              <div className="relative p-8 md:p-8 z-10">
                <h3 className="text-2xl md:text-3xl font-bold mb-8 font-recoleta text-white">Crecé con visibilidad online</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-8">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-left border border-white/20">
                    <h4 className="font-bold mb-3 text-white text-lg">Nuevos pacientes sin esfuerzo</h4>
                    <p className="text-white/80 mb-4 text-sm">Recibí turnos de nuevos pacientes desde nuestra plataforma, sin llamadas ni intermediarios.</p>
                    <ul className="space-y-3">
                      <li className="flex items-center"><div className="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0"><Check className="h-4 w-4 text-white" /></div><span className="text-white text-sm">Recordatorio automático un día antes del turno</span></li>
                      <li className="flex items-center"><div className="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0"><Check className="h-4 w-4 text-white" /></div><span className="text-white text-sm">Sólo pagás cuando el recordatorio es enviado</span></li>
                      <li className="flex items-center"><div className="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0"><Check className="h-4 w-4 text-white" /></div><span className="text-white text-sm">Si el paciente cancela antes, no pagás</span></li>
                      <li className="flex items-center bg-white/30 p-2 rounded-lg mt-4 w-[102%] -ml-2">
                        <div className="h-6 w-6 rounded-full bg-white/30 flex items-center justify-center mr-3 flex-shrink-0">
                          <BellRing className="h-4 w-4 text-white" />
                        </div>
                        <span className="text-white text-sm font-medium">Todos los turnos online incluyen un recordatorio automático</span>
                      </li>
                    </ul>
                    <div className="mt-4 text-center">
                      <span className="inline-flex items-center rounded-full bg-white/10 px-3 py-1 text-xs text-white/80 border border-white/20">Recordatorios: ${pricingConfig.individualReminderPrice} c/u o desde ${lowestPackUnitPrice} con packs</span>
                    </div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-left border border-white/20">
                    <h4 className="font-bold mb-3 text-white text-lg">Mayor visibilidad</h4>
                    <p className="text-white/80 mb-4 text-sm">Sé encontrado por más pacientes y expandí tu práctica médica.</p>
                    <ul className="space-y-3 mb-8">
                      <li className="flex items-center"><div className="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0"><Check className="h-4 w-4 text-white" /></div><span className="text-white text-sm">Perfil destacado en la plataforma de búsqueda de <strong>turnera</strong></span></li>
                      <li className="flex items-center"><div className="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0"><Check className="h-4 w-4 text-white" /></div><span className="text-white text-sm">Aparecé en búsquedas por especialidad y coberturas</span></li>
                      <li className="flex items-center"><div className="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center mr-3 flex-shrink-0"><Check className="h-4 w-4 text-white" /></div><span className="text-white text-sm">Nuestro algoritmo prioriza tu perfil con pacientes relevantes</span></li>
                    </ul>
                    <Button className="w-full mt-auto bg-white text-blue-600 hover:bg-blue-50" onClick={handleRegisterClick}>Empezar a usar turnera</Button>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="border border-gray-200 rounded-lg p-5 text-center hover:border-blue-300 hover:shadow-sm transition-all">
                <h5 className="font-medium mb-2">Sin costos fijos</h5>
                <p className="text-sm text-gray-600">Sólo pagás por los pacientes que reciben un recordatorio</p>
              </div>
              <div className="border border-gray-200 rounded-lg p-5 text-center hover:border-blue-300 hover:shadow-sm transition-all">
                <h5 className="font-medium mb-2">Sin riesgo</h5>
                <p className="text-sm text-gray-600">No hay cargos si el paciente cancela con anticipación</p>
              </div>
              <div className="border border-gray-200 rounded-lg p-5 text-center hover:border-blue-300 hover:shadow-sm transition-all">
                <h5 className="font-medium mb-2">Crecimiento constante</h5>
                <p className="text-sm text-gray-600">Accedé a nuevos pacientes todos los días</p>
              </div>
            </div>
          </div>
          <div id="preguntas-frecuentes" className="max-w-3xl mx-auto rounded-2xl bg-gray-50 md:p-8 relative scroll-mt-20">
            <div className="absolute top-0 right-0 w-32 h-32 bg-blue-100 rounded-full opacity-30 transform translate-x-16 -translate-y-16"></div>
            <h3 className="text-xl md:text-2xl font-bold mb-6 font-recoleta relative z-10">Preguntas frecuentes</h3>
            <div className="space-y-6 relative z-10">
            <div className="p-4 rounded-xl bg-white border border-gray-100 shadow-sm">
                <h4 className="font-semibold mb-2 text-gray-900">¿Qué es un <span className="font-semibold text-blue-600">pack de recordatorios</span>?</h4>
                <p className="text-gray-600">Es un conjunto de recordatorios enviados a tus pacientes por WhatsApp, SMS y Email el día antes de su turno con el fin de minimizar el ausentismo o liberar tu agenda. Al recibirlo, el paciente podrá confirmar o cancelar el turno sin necesidad de llamados telefónicos.</p>
              </div>
              <div className="p-4 rounded-xl bg-white border border-gray-100 shadow-sm">
                <h4 className="font-semibold mb-2 text-gray-900">¿Cuántos recordatorios se descuentan de mi pack cuando se envían por WhatsApp, SMS y Email?</h4>
                <p className="text-gray-600">Uno solo. El envío por los tres medios cuenta como un solo recordatorio, para que te asegures que tu paciente recibió el aviso correctamente.</p>
              </div>
            <div className="p-4 rounded-xl bg-white border border-gray-100 shadow-sm">
                <h4 className="font-semibold mb-2 text-gray-900">¿Cuánto me cuesta un <span className="font-semibold text-blue-600">turno</span> que agendo yo <span className="font-semibold text-blue-600">manualmente</span>?</h4>
                <p className="text-gray-600">Los turnos que agendás manualmente desde la agenda médica no tienen costo. Si contás con un pack de recordatorios activo, se utilizará para enviar los recordatorios a tus pacientes sin ningún cargo adicional. Si no contás con un pack, no se enviará ningún recordatorio.</p>
              </div>
              <div className="p-4 rounded-xl bg-white border border-gray-100 shadow-sm">
                <h4 className="font-semibold mb-2 text-gray-900">¿Cuánto me cuesta un <span className="font-semibold text-blue-600">turno</span> de un paciente que reserva por la <span className="font-semibold text-blue-600">plataforma de búsqueda</span>?</h4>
                <p className="text-gray-600">Cuando un paciente reserva un turno a través de nuestra plataforma de búsqueda, automáticamente se le enviará un recordatorio un día antes de su turno. Este recordatorio tiene un costo de ${pricingConfig.individualReminderPrice}, o se descontará de tu pack de recordatorios activo si ya tenés uno. Sólo pagás cuando el recordatorio es enviado - si el paciente cancela antes, no hay ningún costo para vos.</p>
              </div>
              <div className="p-4 rounded-xl bg-white border border-gray-100 shadow-sm">
                <h4 className="font-semibold mb-2 text-gray-900">¿Hay algún cargo mensual fijo?</h4>
                <p className="text-gray-600">No, turnera no tiene costos fijos. sólo pagás por los recordatorios utilizados y turnos que recibís desde nuestra plataforma de búsqueda y reserva de turnos.</p>
            </div>
            <div className="p-4 rounded-xl bg-white border border-gray-100 shadow-sm">
              <h4 className="font-semibold mb-2 text-gray-900">¿Qué sucede si se agota mi pack de recordatorios?</h4>
              <p className="text-gray-600">Tu pack se renovará automáticamente para que nunca te quedes sin recordatorios. Podés desactivar esta función en cualquier momento desde tu panel de control.</p>
              </div>
              <section className="relative py-6">
        <div className="mx-auto max-w-6xl rounded-2xl bg-gradient-to-tr from-blue-600 to-blue-500 px-6 py-10 text-center text-white">
          <h3 className="font-recoleta text-2xl md:text-3xl">Transformá tu consultorio. Empezá gratis.</h3>
          <p className="mx-auto mt-2 max-w-2xl text-white/90">
            Turnera organiza tus turnos, reduce ausencias y trae pacientes nuevos a tu consultorio.
          </p>
          <div className="mt-6 flex justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50" onClick={handleRegisterClick}>
              Crear cuenta gratis
            </Button>
          </div>
        </div>
        </section>

          </div>
          <div className="text-left pt-20">
            <span className="text-xs">¹ En caso de no contar con un pack de recordatorios activo, los recordatorios por turnos reservados desde la web por los pacientes tendrán el costo equivalente a un recordatorio individual, el cual será enviado automáticamente. En caso de ya contar con un pack de recordatorios, el mismo se descontará del pack sin generar un costo adicional. Costo actual de un recordatorio individual: ${pricingConfig.individualReminderPrice}.</span>
          </div>
        </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-100 py-8">
        <div className="container mx-auto px-10 md:px-6">
          <div className="flex flex-wrap justify-between items-center">
            <div className="w-full md:w-1/3 mb-4 md:mb-0">
              <Link href="/" className="cursor-pointer">
                <Image src="/images/turnera-logo.svg" alt="Turnera" width={120} height={30} />
              </Link>
            </div>
            <div className="w-full md:w-1/3 mb-4 md:mb-0">
              <ul className="flex justify-center space-x-4">
                <li><a href="/terminos-y-condiciones" className="text-gray-600 hover:text-blue-600">Términos y Condiciones</a></li>
                <li><a href="/privacidad" className="text-gray-600 hover:text-blue-600">Privacidad</a></li>
                <li><a href="#" className="text-gray-600 hover:text-blue-600">Contacto</a></li>
              </ul>
            </div>
            <div className="w-full md:w-1/3 text-center md:text-right">
              <p className="text-gray-600">&copy; {new Date().getFullYear()} Turnera. Todos los derechos reservados.</p>
            </div>
          </div>
        </div>
      </footer>

      {/* Registration Dialog */}
      <ProfessionalRegister isOpen={showRegister} onOpenChange={(open) => setShowRegister(open)} />
    </main>
    </>
  )
}