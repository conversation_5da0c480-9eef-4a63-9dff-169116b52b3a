"use client"

import {useEffect, useState} from "react";
import {useRouter} from "next/navigation";
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card";
import {Button} from "@/components/ui/button";
import {
    Building2,
    Calendar,
    ChevronRight,
    MapPin,
    Phone,
    Plus,
    Settings,
    Shield,
    Users,
    LogOut
} from "lucide-react";
import Image from "next/image";
import {useAuth} from "@/hooks/useAuth";
import {getRoleName} from "@/utils/userUtils";
import {UserRole} from "@/types/users";
import {
    getMedicalCenterWorkingDays,
    MedicalCenterRoleForEmployeeUser
} from "@/types/MedicalCenter/medicalCenterRoleForEmployeeUser";
import {PatientUserPill} from "@/components/ui/PatientUserPill";
import dynamic from "next/dynamic";

const NewMedicalCenterDialog = dynamic(
  () => import("@/components/dialogs/new-medical-center-dialog"),
  { ssr: false }
);

export default function MedicalCenterSelectorPage() {
    const [medicalCentersForEmployeeUser, setMedicalCentersForEmployeeUser] = useState<MedicalCenterRoleForEmployeeUser[]>([]);
    const [loading, setLoading] = useState(true);
    const [isNewMedicalCenterDialogOpen, setIsNewMedicalCenterDialogOpen] = useState(false);
    const router = useRouter();
    const {currentUser, isAuthenticated, isLoading, logout, getUserMedicalCenters} = useAuth();

    // Check authentication and load medical centers
    useEffect(() => {
        if (isLoading) return;

        if (!isAuthenticated || !currentUser) {
            // Not authenticated, redirect to login
            router.push("/plataforma/establecimiento/login");
            return;
        }

        // Get all medical centers the user has access to
        const userMedicalCenters: MedicalCenterRoleForEmployeeUser[] = getUserMedicalCenters();


        // Load medical centers from storage
        setLoading(true);


        setMedicalCentersForEmployeeUser(userMedicalCenters);
        setLoading(false);
    }, [isAuthenticated, currentUser, router, isLoading, getUserMedicalCenters]);

    const handleSelectMedicalCenter = (medicalCenterId: number) => {
        router.push(`/plataforma/establecimiento/${medicalCenterId}`);
    };

    const handleLogout = () => {
        logout();
    };



    if (loading || isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white flex items-center justify-center p-6">
                <div className="bg-white rounded-2xl shadow-2xl shadow-slate-400/20 p-10 text-center max-w-md w-full border border-slate-200">
                    <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <div className="w-8 h-8 border-4 border-slate-600 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                    <h3 className="text-[1.35rem] font-semibold text-slate-800 mb-2">Cargando</h3>
                    <p className="text-slate-600">Preparando panel de administración...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-white flex flex-col">
            {/* Top Bar */}
            <div className="sticky top-0 z-40 bg-white">
                <div className="max-w-8xl mx-auto grid grid-cols-[1fr_auto] items-center gap-3 px-12 py-2">
                    <div className="flex items-center gap-3">
                        <Image src="/images/turnera-logo.svg" alt="Turnera" width={120} height={32} className="h-7 w-auto align-middle relative -top-[1.5px]"/>
                        <div className="h-7 w-px bg-slate-200"/>
                        <p className="text-[1.1rem] leading-none text-slate-700 font-semibold">Establecimientos</p>
                    </div>
                    <div className="justify-self-end">
                        <PatientUserPill
                            currentUser={currentUser}
                            currentPatient={null}
                            logout={handleLogout}
                            variant="light"
                        />
                    </div>
                </div>
            </div>

            {/* Main Content Box */}
            <main className="flex-1">
                <div className="bg-gradient-to-b from-[#f3f8fc] to-[#f4f8fa] border-t border-[#e5e9f2] shadow-inner rounded-t-3xl w-full px-6 py-10 flex flex-col min-h-[calc(100vh-64px)]">
                    {/* Hero */}
                    <div className="text-center mb-6">
                        <div className="inline-flex items-center gap-2 bg-white px-4 py-2 rounded-full border border-slate-200 shadow-sm">
                            <Shield className="h-4 w-4 text-slate-700"/>
                            <span className="text-sm font-medium text-slate-800">Panel administrativo</span>
                        </div>
                        <p className="mt-3 text-[0.95rem] text-slate-600 max-w-2xl mx-auto">Gestione y administre los establecimientos médicos bajo su supervisión.</p>
                    </div>

                    {medicalCentersForEmployeeUser.length === 0 ? (
                        currentUser?.roles.includes(UserRole.EMPLOYEE_USER) ? (
                            // EMPLOYEE_USER with no medical centers - show placeholder card
                            <div className="flex flex-wrap justify-center gap-7 sm:gap-8 max-w-6xl mx-auto">
                                <div className="group bg-white/50 border-2 border-dashed border-blue-300/60 shadow-sm rounded-2xl overflow-hidden transition-all duration-300 hover:border-blue-400/80 card-grid-item w-[22rem] min-h-[400px] flex flex-col">
                                    <div className="p-7 pb-5 relative flex-1 flex flex-col">
                                        <div className="flex items-start justify-between mb-3">
                                            <div className="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center group-hover:bg-blue-100 transition-colors duration-300">
                                                <Building2 className="h-6 w-6 text-blue-400 group-hover:text-blue-500 transition-colors duration-300"/>
                                            </div>
                                            <div className="text-right">
                                                <span className="inline-block bg-blue-50 text-blue-400 text-xs font-medium px-3 py-1 rounded-full">#01</span>
                                            </div>
                                        </div>

                                        <div className="flex-1 flex flex-col justify-center items-center text-center">
                                            <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-100 transition-colors duration-300">
                                                <Building2 className="h-8 w-8 text-blue-400 group-hover:text-blue-500 transition-colors duration-300"/>
                                            </div>
                                            <h3 className="text-[1.05rem] font-semibold text-blue-600 mb-2 group-hover:text-blue-700 transition-colors duration-300">
                                                Agregá tu primer establecimiento
                                            </h3>
                                            <p className="text-blue-500/70 text-sm leading-relaxed max-w-48">
                                                Comenzá a gestionar tu consultorio o centro médico.
                                            </p>
                                        </div>
                                    </div>

                                    <div className="p-7 pt-3">
                                        <Button
                                            className="w-full bg-blue-500 hover:bg-blue-600 text-white h-11 rounded-xl font-medium transition-all duration-300 group-hover:bg-blue-600 flex items-center justify-center gap-1 border-2 border-dashed border-blue-300/40"
                                            onClick={() => setIsNewMedicalCenterDialogOpen(true)}
                                        >
                                            <Plus className="h-4 w-4"/>
                                            <span>Agregar</span>
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            // Non-EMPLOYEE_USER with no medical centers - show error message
                            <div className="flex justify-center">
                                <div className="max-w-lg w-full">
                                    <div className="bg-white rounded-2xl shadow-lg p-10 text-center border border-slate-200">
                                        <div className="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-6">
                                            <Building2 className="h-10 w-10 text-red-400"/>
                                        </div>
                                        <h3 className="text-[1.25rem] font-semibold text-slate-800 mb-3">Sin acceso a establecimientos</h3>
                                        <p className="text-slate-600 mb-8 leading-relaxed">No tiene permisos administrativos en ningún establecimiento. Contacte al administrador del sistema.</p>
                                        <Button onClick={handleLogout} className="bg-red-600 hover:bg-red-700 text-white px-8 h-11 rounded-xl">
                                            <LogOut className="h-4 w-4 mr-2"/>
                                            Cerrar sesión
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )
                    ) : (
                        <div className="flex flex-wrap justify-center gap-7 sm:gap-8 max-w-6xl mx-auto">
                            {medicalCentersForEmployeeUser.map((center, index) => {
                                const userRole = center.role;
                                const workingDays = getMedicalCenterWorkingDays(center);

                                return (
                                    <Card
                                        key={center.id}
                                        className="group bg-white border border-[#e5e9f2] shadow-sm hover:shadow-md rounded-2xl overflow-hidden transition-all duration-300 hover:-translate-y-0.5 card-grid-item w-[22rem]"
                                    >
                                        <CardHeader className="p-7 pb-5 relative">
                                            <div className="flex items-start justify-between mb-3">
                                                <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center group-hover:bg-slate-700 transition-colors duration-300">
                                                    <Building2 className="h-6 w-6 text-slate-600 group-hover:text-white transition-colors duration-300"/>
                                                </div>
                                                <div className="text-right">
                                                    <span className="inline-block bg-slate-100 text-slate-600 text-xs font-medium px-3 py-1 rounded-full">#{String(index + 1).padStart(2, '0')}</span>
                                                </div>
                                            </div>

                                            <CardTitle className="text-[1.05rem] font-semibold text-slate-800 mb-1 group-hover:text-slate-700 transition-colors duration-300">
                                                {center.name}
                                            </CardTitle>
                                            <CardDescription className="text-gray-600">
                                                <div className="flex items-start gap-2">
                                                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0"/>
                                                    <span className="text-sm leading-relaxed">{center.address}</span>
                                                </div>
                                            </CardDescription>
                                        </CardHeader>

                                        <CardContent className="px-7 pb-5">
                                            <div className="space-y-3.5">
                                                {center.phoneNumber && (
                                                    <div className="flex items-center gap-3 p-3 bg-slate-50 rounded-lg border border-slate-200/60">
                                                        <Phone className="h-4 w-4 text-slate-500"/>
                                                        <span className="text-sm text-slate-700 font-medium">{center.phoneNumber}</span>
                                                    </div>
                                                )}

                                                {userRole && (
                                                    <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-100">
                                                        <Shield className="h-4 w-4 text-blue-600"/>
                                                        <div>
                                                            <span className="text-sm text-blue-800 font-medium">{getRoleName(center.role)}</span>
                                                            <p className="text-xs text-blue-600">Permisos administrativos</p>
                                                        </div>
                                                    </div>
                                                )}

                                                <div className="grid grid-cols-2 gap-3">
                                                    <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-100">
                                                        <Calendar className="h-4 w-4 text-green-600"/>
                                                        <div>
                                                            <p className="text-sm font-medium text-green-800">{workingDays}</p>
                                                            <p className="text-xs text-green-600">Días activos</p>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg border border-purple-100">
                                                        <Users className="h-4 w-4 text-purple-600"/>
                                                        <div>
                                                            <p className="text-sm font-medium text-purple-800">{center.doctorsCount}</p>
                                                            <p className="text-xs text-purple-600">Profesionales</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>

                                        <CardFooter className="p-7 pt-3">
                                            <Button
                                                onClick={() => handleSelectMedicalCenter(center.id)}
                                                className="w-full bg-slate-700 hover:bg-slate-800 text-white h-11 rounded-xl font-medium transition-all duration-300 group-hover:bg-slate-800 flex items-center justify-center gap-2"
                                            >
                                                <Settings className="h-4 w-4"/>
                                                <span>Administrar</span>
                                                <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300"/>
                                            </Button>
                                        </CardFooter>
                                    </Card>
                                );
                            })}
                        </div>
                    )}
                </div>
            </main>

            {/* New Medical Center Dialog */}
            <NewMedicalCenterDialog
                isOpen={isNewMedicalCenterDialogOpen}
                onClose={() => setIsNewMedicalCenterDialogOpen(false)}
                onSuccess={(medicalCenterId) => { // TODO FACU: buscar usermedicalcenters nuevamente
                    // Refresh the medical centers list
                    const userMedicalCenters: MedicalCenterRoleForEmployeeUser[] = getUserMedicalCenters();
                    setMedicalCentersForEmployeeUser(userMedicalCenters);
                    setIsNewMedicalCenterDialogOpen(false);
                }}
            />

            {/* Animations */}
            <style jsx global>{`
                .card-grid-item {
                    opacity: 0;
                    animation: slideInUp 0.6s ease-out forwards;
                }

                .card-grid-item:nth-child(1) { animation-delay: 0.06s; }
                .card-grid-item:nth-child(2) { animation-delay: 0.12s; }
                .card-grid-item:nth-child(3) { animation-delay: 0.18s; }
                .card-grid-item:nth-child(4) { animation-delay: 0.24s; }
                .card-grid-item:nth-child(5) { animation-delay: 0.30s; }
                .card-grid-item:nth-child(6) { animation-delay: 0.36s; }

                @keyframes slideInUp {
                    from { opacity: 0; transform: translateY(30px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            `}</style>
        </div>
    );
}