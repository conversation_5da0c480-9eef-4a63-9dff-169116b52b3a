"use client"

import { useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { useAuth } from "@/hooks/useAuth"

export default function EstablishmentLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const router = useRouter()
  const { currentUser, isAuthenticated, isLoading } = useAuth()

  // Authentication check
  useEffect(() => {
    // Skip authentication check for login page and admin page
    if (pathname === "/plataforma/establecimiento/login" || pathname.startsWith("/admin")) {
      return
    }

    if (!isLoading && !isAuthenticated) {
      // Not authenticated, redirect to login
      router.push('/plataforma/login')
      return
    }

    if (!isLoading && isAuthenticated && currentUser) {
      // No need to redirect users based on their role
      // Users can have multiple roles in different medical centers
      // Access to specific medical centers is checked in the [medicalCenterId] layout
    }
  }, [isAuthenticated, isLoading, currentUser, router, pathname])

  return (
    <>
      {children}
    </>
  )
}
