"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { Shield, AlertTriangle, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/hooks/useAuth"
import { UserRole } from "@/types/users"

export default function UnauthorizedPage() {
  const router = useRouter()
  const { currentUser } = useAuth()
  const [redirectPath, setRedirectPath] = useState<string>("")

  useEffect(() => {
    // Determine the appropriate redirect path based on user role
    if (currentUser) {
      if (currentUser.roles === UserRole.DOCTOR) {
        setRedirectPath(`/plataforma/profesional/${currentUser.doctorId}`)
      } else if (currentUser.roles === UserRole.PATIENT) {
        setRedirectPath(`/plataforma/paciente`)
      } else if (currentUser.roles === UserRole.ADMIN || currentUser.roles === UserRole.SUPERUSER) {
        setRedirectPath(`/plataforma/establecimiento/${currentUser.medicalCenterId}`)
      } else {
        setRedirectPath(`/plataforma/establecimiento/${currentUser.medicalCenterId}`)
      }
    } else {
      setRedirectPath("/plataforma")
    }
  }, [currentUser])

  const handleGoBack = () => {
    if (redirectPath) {
      router.push(redirectPath)
    } else {
      router.push("/plataforma")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 via-white to-blue-50 flex flex-col items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="bg-red-600 p-6 flex justify-center">
          <div className="bg-white/10 rounded-full p-3">
            <Shield className="h-12 w-12 text-white" />
          </div>
        </div>

        <div className="p-8 text-center">
          <div className="inline-flex items-center justify-center bg-red-100 rounded-full p-1 mb-4">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-1" />
            <span className="text-sm font-medium text-red-600 px-2">Acceso Denegado</span>
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-3">No tenés autorización</h1>

          <p className="text-gray-600 mb-6">
            No tenés los permisos necesarios para acceder a esta página. Esta sección está reservada para usuarios con roles específicos.
          </p>

          <div className="flex justify-center">
            <Button
              onClick={handleGoBack}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Volver a mi área
            </Button>
          </div>
        </div>

        <div className="bg-gray-50 px-8 py-4 border-t border-gray-100 flex items-center justify-center">
          <Image
            src="/images/turnera-logo.svg"
            alt="Turnera Logo"
            width={120}
            height={30}
            className="h-6 w-auto opacity-70"
          />
        </div>
      </div>
    </div>
  )
}
